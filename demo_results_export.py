#!/usr/bin/env python3
"""
Demonstrace exportu výsledků z interaktivního preview.
"""

import pandas as pd
import preview_qt as preview
import utils
from OCR import ocr

def demo_results_export():
    """Demonstrace jak získat výsledky z interaktivního preview."""
    
    print("🎯 Demo: Export výsledků z interaktivního preview")
    print("=" * 50)
    
    # Načteme dokument
    file_path = 'data/F3.pdf'
    
    print("1. Načítání a zpracování dokumentu...")
    df = ocr.do(file_path)
    df = utils.merge_texts(df)
    utils.classify_batch_values(df)
    df = utils.clean_texts(df)
    
    # Zobrazíme původní stav
    print(f"   ✓ Celkem prvků: {len(df)}")
    interactive_count = len(df[df['value_class'] > 0])
    print(f"   ✓ Interaktivních prvků: {interactive_count}")
    
    # Zkontrolujeme, zda už existuje result_class sloupec
    has_result_class_before = 'result_class' in df.columns
    print(f"   ✓ result_class sloupec před interakcí: {'ANO' if has_result_class_before else 'NE'}")
    
    print("\n2. Spouštím interaktivní preview...")
    print("   💡 INSTRUKCE:")
    print("   - Najděte prvky s výběrovými seznamy (vlevo od bounding boxů)")
    print("   - Vyberte kategorii z dropdown menu pro několik prvků")
    print("   - Klikněte tlačítko 'Export Results' pro export do CSV (složka Results)")
    print("   - Zavřete okno pro získání výsledků do této aplikace")
    
    # Spustíme interaktivní preview s return_results=True
    updated_df = preview.show_document_qt(file_path, df, return_results=True)
    
    print("\n3. Analýza výsledků...")
    
    if updated_df is not None:
        # Zkontrolujeme result_class sloupec
        has_result_class_after = 'result_class' in updated_df.columns
        print(f"   ✓ result_class sloupec po interakci: {'ANO' if has_result_class_after else 'NE'}")
        
        if has_result_class_after:
            # Statistiky
            total = len(updated_df)
            classified = len(updated_df[updated_df['result_class'].notna()])
            unclassified = total - classified
            
            print(f"   ✓ Celkem prvků: {total}")
            print(f"   ✓ Klasifikovaných: {classified}")
            print(f"   ✓ Neklasifikovaných: {unclassified}")
            
            if classified > 0:
                print(f"\n   📊 Rozložení podle result_class:")
                result_counts = updated_df['result_class'].value_counts()
                for category, count in result_counts.items():
                    print(f"      {category}: {count} prvků")
                
                # Ukázka klasifikovaných prvků
                print(f"\n   🔍 Ukázka klasifikovaných prvků:")
                sample = updated_df[updated_df['result_class'].notna()][['text', 'value_class', 'result_class']].head(5)
                for _, row in sample.iterrows():
                    value_class_name = utils.get_value_class_name(row['value_class'])
                    result_class_name = utils.get_result_class_name(row['result_class'])

                    print(f"      '{row['text']}' ({value_class_name}) → {row['result_class']} ({result_class_name})")
                
                # Export do CSV pomocí nové funkce
                try:
                    filtered_output = utils.export_filtered_results(updated_df, file_path)
                    print(f"\n   💾 Filtrované výsledky exportovány do: {filtered_output}")
                except Exception as e:
                    print(f"\n   ❌ Chyba při filtovaném exportu: {e}")

                # Také uložíme všechna data pro porovnání
                output_file = 'demo_results_all.csv'
                updated_df.to_csv(output_file, index=False)
                print(f"   💾 Všechna data exportována do: {output_file}")
                
                # Zobrazíme strukturu výsledného DataFrame
                print(f"\n   📋 Struktura výsledného DataFrame:")
                print(f"      Sloupce: {list(updated_df.columns)}")
                print(f"      Shape: {updated_df.shape}")
                
            else:
                print(f"\n   ⚠️  Žádné prvky nebyly klasifikovány")
                print(f"      Zkuste znovu spustit a vybrat kategorie z dropdown menu")
        else:
            print(f"   ❌ result_class sloupec nebyl vytvořen!")
    else:
        print(f"   ❌ Žádné výsledky nebyly vráceny!")
    
    print(f"\n✅ Demo dokončeno!")
    return updated_df

if __name__ == "__main__":
    result_df = demo_results_export()
