#!/usr/bin/env python3
"""
Test interaktivního exportu z preview_qt.py.
Testuje export tlačítkem "Export Results" v interaktivním preview.
"""

import sys
import os
sys.path.append('.')

import pandas as pd
import utils
import preview
from OCR import ocr

def create_test_data_with_classifications():
    """Vytvoří testovací data s realistickými klas<PERSON>."""
    test_data = {
        'text': [
            'FAKTURA',           # key_class=1, result_class=1 -> EXPORT
            '2024-01-15',        # key_class=0, result_class=2 -> NEEXPORT (key_class=0)
            'F2024001',          # key_class=5, result_class=1 -> EXPORT
            'Dodavatel s.r.o.',  # key_class=9, result_class=0 -> NEEXPORT (result_class=0)
            '31.12.2024',        # key_class=13, result_class=3 -> EXPORT
            'Běžný text',        # key_class=0, result_class=0 -> NEEXPORT (oba=0)
            'CZ12345678',        # key_class=20, result_class=7 -> EXPORT
            '123456789',         # key_class=6, result_class=6 -> EXPORT
            'Celkem k úhradě',   # key_class=15, result_class=17 -> EXPORT
            'Poznámka',          # key_class=0, result_class=0 -> NEEXPORT (oba=0)
            '25000.50',          # key_class=0, result_class=16 -> NEEXPORT (key_class=0)
            'IČO: 12345678',     # key_class=18, result_class=8 -> EXPORT
        ],
        'left': [50, 200, 350, 500, 50, 200, 350, 500, 50, 200, 350, 500],
        'top': [50, 50, 50, 50, 150, 150, 150, 150, 250, 250, 250, 250],
        'width': [120, 100, 120, 150, 100, 120, 140, 130, 100, 150, 120, 140],
        'height': [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25],
        'key_class': [1, 0, 5, 9, 13, 0, 20, 6, 15, 0, 0, 18],
        'value_class': [0, 1, 5, 0, 1, 0, 7, 8, 3, 0, 3, 8],
        'result_class': [1, 2, 1, 0, 3, 0, 7, 6, 17, 0, 16, 8]
    }
    
    return pd.DataFrame(test_data)

def test_interactive_export():
    """Testuje interaktivní export pomocí preview_qt."""
    print("🎯 Test interaktivního exportu")
    print("=" * 50)
    
    # Vytvoříme testovací data
    df = create_test_data_with_classifications()
    
    print("\n1. Testovací data připravena:")
    print(f"   Celkem prvků: {len(df)}")
    
    # Spočítáme očekávané výsledky
    expected_filtered = len(df[(df['key_class'] > 0) & (df['result_class'] > 0)])
    print(f"   Prvky k exportu (key_class > 0 AND result_class > 0): {expected_filtered}")
    
    print("\n   Detailní přehled:")
    print("   Text                | key_class | result_class | Export?")
    print("   " + "-" * 60)
    for i, row in df.iterrows():
        export_status = "ANO" if (row['key_class'] > 0 and row['result_class'] > 0) else "NE"
        print(f"   {row['text']:<18} | {row['key_class']:>9} | {row['result_class']:>12} | {export_status}")
    
    print(f"\n2. Spouštím interaktivní preview...")
    print(f"   💡 INSTRUKCE:")
    print(f"   - Otevře se interaktivní okno s dokumentem")
    print(f"   - Uvidíte bounding boxy s dropdown seznamy")
    print(f"   - Klikněte na tlačítko 'Export Results' v toolbar")
    print(f"   - Export se provede automaticky do složky Results")
    print(f"   - Zavřete okno pro pokračování")
    
    # Dummy file path pro test
    test_file_path = "test_interactive.pdf"
    
    try:
        # Spustíme interaktivní preview
        # Poznámka: Toto otevře GUI okno
        result_df = preview.show_document_qt(test_file_path, df, return_results=True)
        
        print(f"\n3. Výsledky po zavření preview:")
        if result_df is not None:
            print(f"   ✓ DataFrame vrácen s {len(result_df)} řádky")
            
            # Zkontrolujeme, zda byl vytvořen export soubor
            expected_output = "Results/test_interactive.csv"
            if os.path.exists(expected_output):
                exported_df = pd.read_csv(expected_output)
                print(f"   ✅ Export soubor nalezen: {expected_output}")
                print(f"   📊 Exportováno řádků: {len(exported_df)}")
                
                # Zkontrolujeme řádek "page"
                page_rows = exported_df[exported_df['text'] == 'page']
                if len(page_rows) == 1:
                    print(f"   ✓ Řádek 'page' správně přidán")
                else:
                    print(f"   ⚠️  Problém s řádkem 'page'")
                    
                # Zobrazíme ukázku
                print(f"\n   📋 Ukázka exportovaných dat:")
                for i, row in exported_df.head().iterrows():
                    print(f"      {row['text']:<18} | key:{row['key_class']:>2} | result:{row['result_class']:>2}")
                    
            else:
                print(f"   ⚠️  Export soubor nebyl nalezen: {expected_output}")
                print(f"      Možná jste nezvolili 'Export Results' v GUI")
        else:
            print(f"   ⚠️  Žádný DataFrame nebyl vrácen")
            
    except Exception as e:
        print(f"   ❌ Chyba při spouštění preview: {e}")
        print(f"      Možná není dostupné PyQt5 nebo GUI prostředí")

def test_with_real_document():
    """Testuje export s reálným dokumentem."""
    print("\n📄 Test s reálným dokumentem")
    print("=" * 50)
    
    file_path = 'data/F3.pdf'
    
    if not os.path.exists(file_path):
        print(f"   ⚠️  Soubor {file_path} neexistuje, přeskakujem test")
        return
    
    print(f"\n1. Načítání dokumentu: {file_path}")
    try:
        # Načteme a zpracujeme dokument
        df = ocr.do(file_path)
        df = utils.merge_texts(df)
        utils.classify_batch_values(df)
        df = utils.clean_texts(df)
        
        # Přidáme nějaké testovací klasifikace
        df['key_class'] = 0
        df['result_class'] = 0
        
        # Nastavíme několik prvků pro export
        if len(df) >= 5:
            df.loc[0, 'key_class'] = 1   # FAKTURA
            df.loc[0, 'result_class'] = 1
            df.loc[1, 'key_class'] = 5   # Číslo faktury
            df.loc[1, 'result_class'] = 1
            df.loc[2, 'key_class'] = 12  # Datum vystavení
            df.loc[2, 'result_class'] = 2
            df.loc[3, 'key_class'] = 15  # Celkem k úhradě
            df.loc[3, 'result_class'] = 17
            df.loc[4, 'key_class'] = 9   # Dodavatel
            df.loc[4, 'result_class'] = 0  # Nebude exportován
        
        print(f"   ✓ Načteno prvků: {len(df)}")
        export_count = len(df[(df['key_class'] > 0) & (df['result_class'] > 0)])
        print(f"   ✓ Prvky k exportu: {export_count}")
        
        print(f"\n2. Spouštím interaktivní preview...")
        print(f"   💡 INSTRUKCE:")
        print(f"   - Otevře se reálný dokument s OCR výsledky")
        print(f"   - Některé prvky už mají nastavené klasifikace")
        print(f"   - Můžete upravit klasifikace pomocí dropdown seznamů")
        print(f"   - Klikněte 'Export Results' pro export")
        print(f"   - Zavřete okno pro pokračování")
        
        # Spustíme interaktivní preview
        result_df = preview.show_document_qt(file_path, df, return_results=True)
        
        print(f"\n3. Kontrola výsledků:")
        expected_output = f"Results/{os.path.splitext(os.path.basename(file_path))[0]}.csv"
        
        if os.path.exists(expected_output):
            exported_df = pd.read_csv(expected_output)
            print(f"   ✅ Export úspěšný!")
            print(f"   📁 Soubor: {expected_output}")
            print(f"   📊 Řádků: {len(exported_df)}")
            
            # Zkontrolujeme strukturu
            page_rows = exported_df[exported_df['text'] == 'page']
            data_rows = exported_df[exported_df['text'] != 'page']
            
            print(f"   📋 Struktura:")
            print(f"      Data řádky: {len(data_rows)}")
            print(f"      Page řádky: {len(page_rows)}")
            
            if len(page_rows) == 1:
                page_row = page_rows.iloc[0]
                print(f"      Page rozměry: {page_row['width']} x {page_row['height']}")
            
        else:
            print(f"   ⚠️  Export soubor nebyl vytvořen")
            print(f"      Zkontrolujte, zda jste klikli na 'Export Results'")
            
    except Exception as e:
        print(f"   ❌ Chyba: {e}")

def main():
    """Hlavní funkce testu."""
    print("🚀 Test interaktivního CSV exportu")
    print("=" * 60)
    
    # Test s testovacími daty
    test_interactive_export()
    
    # Test s reálným dokumentem
    test_with_real_document()
    
    print(f"\n✅ Testy dokončeny!")
    print(f"📁 Zkontrolujte složku 'Results' pro exportované soubory")

if __name__ == "__main__":
    main()
