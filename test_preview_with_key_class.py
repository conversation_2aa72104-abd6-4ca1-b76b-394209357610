#!/usr/bin/env python3
"""
Test preview s daty o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> key_class hodnoty.
"""

import pandas as pd
import sys
import os

def create_test_data_with_key_class():
    """<PERSON>yt<PERSON><PERSON><PERSON> testovací data s key_class hodnotami."""
    
    # Vytvoříme testovací DataFrame s key_class hodnotami
    test_data = {
        'text': [
            'FAKTURA',               # key_class: 1 (Faktura)
            '2024-001',              # key_class: 5 (<PERSON><PERSON><PERSON>)  
            '25.12.2023',            # key_class: 12 (<PERSON><PERSON>)
            'Dodavatel s.r.o.',      # key_class: 9 (Dodavatel)
            'CZ12345678',            # key_class: 20 (DIČ)
            'VS: 123456',            # key_class: 6 (<PERSON><PERSON><PERSON>n<PERSON> symbol)
            '15 000,00 Kč',          # key_class: 15 (<PERSON><PERSON><PERSON>)
            'IBAN: CZ65...',         # key_class: 21 (IBAN)
            'Běžný text',            # key_class: 0 (<PERSON><PERSON><PERSON><PERSON><PERSON>)
            'Datum splatnosti:',     # key_class: 13 (<PERSON><PERSON> splatnosti)
        ],
        'left': [50, 200, 350, 500, 50, 200, 350, 500, 50, 200],
        'top': [50, 50, 50, 50, 150, 150, 150, 150, 250, 250],
        'width': [120, 100, 120, 150, 100, 120, 140, 130, 100, 150],
        'height': [25, 25, 25, 25, 25, 25, 25, 25, 25, 25],
        'class': [None] * 10,  # Starý sloupec
        'key_class': [1, 5, 12, 9, 20, 6, 15, 21, 0, 13],  # Key class hodnoty
        'value_class': [0, 5, 1, 0, 7, 8, 3, 6, 0, 0],     # Value class pro porovnání
        'result_class': [0, 1, 2, 0, 7, 6, 17, 11, 0, 3]   # Result class pro dropdown
    }
    
    return pd.DataFrame(test_data)

def test_preview_with_key_class():
    """Test preview s key_class daty."""
    
    print("🎯 Test preview s key_class daty")
    print("=" * 45)
    
    # Vytvoříme testovací data
    df = create_test_data_with_key_class()
    
    print("1. Testovací data:")
    print("   Text                 | key_class | Očekávaný červený popisek")
    print("   " + "-" * 65)
    
    import utils
    for i, row in df.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        display = "ANO" if row['key_class'] > 0 else "NE"
        print(f"   {row['text']:<19} | {row['key_class']:>9} | {key_name} ({display})")
    
    # Uložíme testovací data
    output_file = 'test_key_class_data.csv'
    df.to_csv(output_file, index=False)
    print(f"\n2. Testovací data uložena do: {output_file}")
    
    print("\n3. Očekávané zobrazení v preview:")
    key_elements = df[df['key_class'] > 0]
    print(f"   Červené popisky nad bounding boxy ({len(key_elements)} prvků):")
    for i, row in key_elements.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        print(f"      '{row['text']}' → '{key_name}' (červeně)")
    
    value_elements = df[df['value_class'] > 0]
    print(f"\n   Modré popisky pod bounding boxy ({len(value_elements)} prvků):")
    for i, row in value_elements.iterrows():
        value_name = utils.get_value_class_name(row['value_class'])
        print(f"      '{row['text']}' → '{value_name}' (modře)")
    
    interactive_elements = df[df['value_class'] > 0]
    print(f"\n   Dropdown seznamy vlevo od bounding boxů ({len(interactive_elements)} prvků):")
    for i, row in interactive_elements.iterrows():
        result_name = utils.get_result_class_name(row['result_class'])
        print(f"      '{row['text']}' → aktuálně '{result_name}'")
    
    print("\n4. Spuštění preview:")
    print(f"   python3 -c \"")
    print(f"import pandas as pd")
    print(f"from preview_qt import preview_document")
    print(f"df = pd.read_csv('{output_file}')")
    print(f"preview_document(df, 'Testovací dokument s key_class')\"")
    
    print("\n5. Kontrola oprav v preview_qt.py:")
    print("   ✅ key_class se načítá ze správného sloupce 'key_class'")
    print("   ✅ Podmínka změněna na 'if key_class > 0'")
    print("   ✅ Zobrazuje se název z číselníku místo čísla")
    print("   ✅ Červená barva pro key_class popisky")
    print("   ✅ Pozice nad bounding boxem (y - 25)")
    
    print("\n✅ Test připraven!")
    print(f"\n💡 Pro vizuální test spusťte:")
    print(f"   python3 -c \"import pandas as pd; from preview_qt import preview_document; df = pd.read_csv('{output_file}'); preview_document(df, 'Test key_class')\"")

if __name__ == "__main__":
    test_preview_with_key_class()
