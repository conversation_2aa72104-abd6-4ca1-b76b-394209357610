#!/usr/bin/env python3
"""
Test script pro interaktivní preview s výběrovými seznamy.
"""

import sys
import pandas as pd
from preview_qt import show_document_qt
import utils
from OCR import ocr

def test_interactive_preview():
    """Test interaktivního preview s výběrovými seznamy."""
    
    print("🧪 Test interaktivního preview")
    print("=" * 40)
    
    # Načteme testovací dokument
    file_path = 'data/F3.pdf'
    
    print("1. Načítání dokumentu...")
    df = ocr.do(file_path)
    print(f"   ✓ Načteno {len(df)} raw elementů")
    
    print("2. Zpracování textu...")
    df = utils.merge_texts(df)
    print(f"   ✓ Po sloučení: {len(df)} elementů")
    
    print("3. Klasifikace hodnot...")
    utils.classify_batch_values(df)
    print(f"   ✓ Klasifikace dokončena")
    
    # Zobrazíme statistiky value_class
    value_class_counts = df['value_class'].value_counts().sort_index()
    print(f"   📊 Rozložení value_class:")
    for value_class, count in value_class_counts.items():
        if value_class > 0:  # Pouze prvky, které budou mít výběrové seznamy
            print(f"      value_class {value_class}: {count} prvků")
    
    interactive_elements = len(df[df['value_class'] > 0])
    print(f"   🎯 Celkem {interactive_elements} prvků bude mít výběrové seznamy")
    
    print("4. Čištění textu...")
    df = utils.clean_texts(df)
    print(f"   ✓ Čištění dokončeno")
    
    print("\n🚀 Spouštím interaktivní preview...")
    print("   💡 Tip: Prvky s value_class > 0 budou mít výběrové seznamy vlevo")
    print("   💡 Tip: Klasifikujte prvky a pak zavřete okno pro získání výsledků")

    # Spustíme interaktivní preview a získáme výsledky
    updated_df = show_document_qt(file_path, df, return_results=True)

    print("\n📊 Analýza výsledků:")
    if updated_df is not None and 'result_class' in updated_df.columns:
        classified_count = len(updated_df[updated_df['result_class'].notna()])
        total_count = len(updated_df)

        print(f"   ✓ Klasifikováno: {classified_count}/{total_count} prvků")

        if classified_count > 0:
            print(f"   📋 Rozložení klasifikace:")
            for class_name, count in updated_df['result_class'].value_counts().items():
                print(f"      {class_name}: {count}")

            # Ukázka klasifikovaných prvků
            sample = updated_df[updated_df['result_class'].notna()][['text', 'result_class']].head(3)
            print(f"   🔍 Ukázka:")
            for _, row in sample.iterrows():
                print(f"      '{row['text']}' → {row['result_class']}")

        # Uložení výsledků
        updated_df.to_csv('test_results.csv', index=False)
        print(f"   💾 Výsledky uloženy do: test_results.csv")
    else:
        print("   ⚠️  Žádné výsledky nebo chybí result_class sloupec")

if __name__ == "__main__":
    test_interactive_preview()
