#!/usr/bin/env python3
"""
Document preview module with PyQt5 interactive viewer.
Provides superior document viewing with smooth zoom/pan and reliable bounding box overlays.
"""

import cv2
import numpy as np
from pdf2image import convert_from_path

# Try to import PyQt5 for interactive viewing
try:
    from preview_qt import show_document_qt, show_ocr_result_qt
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False
    print("Warning: PyQt5 not available. Interactive features will be limited.")

def show_document(file_path, df):
    """
    Original OpenCV-based document display.
    Kept for compatibility and as fallback.
    """
    images = convert_from_path(file_path, dpi=300, output_folder=None)
    for q, image in enumerate(images):
        image = np.array(image)
        for i in range(len(df)):
            try:
                # Handle both old and new column names for compatibility
                if 'key_class' in df.columns:
                    (t, x, y, w, h, ck, cv) = (df.loc[i, 'text'], df.loc[i, 'left'], df.loc[i, 'top'], 
                                             df.loc[i, 'width'], df.loc[i, 'height'], 
                                             df.loc[i, 'key_class'], df.loc[i, 'value_class'])
                else:
                    (t, x, y, w, h) = (df.loc[i, 'char'], df.loc[i, 'left'], df.loc[i, 'top'],
                                             df.loc[i, 'width'], df.loc[i, 'height'])
                
                image = cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 3)
                if t is not None:
                    cv2.putText(image, str(t), (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
                #if cv > 0:
                 #   cv2.putText(image, str(cv), (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 3)
            except Exception as e:
                print(f"Error processing row {i}: {e}")
                continue
                
        cv2.imshow('captured text', image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

def show_ocr_result(file_path, df):
    """
    Original OpenCV-based OCR result display.
    Kept for compatibility and as fallback.
    """
    images = convert_from_path(file_path, dpi=300, output_folder=None)
    for q, image in enumerate(images):
        image = np.array(image)
        for i in range(len(df)):
            try:
                (t, x, y, w, h) = (df.loc[i, 'text'], df.loc[i, 'left'], df.loc[i, 'top'], 
                                 df.loc[i, 'width'], df.loc[i, 'height'])
                image = cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 3)
            except Exception as e:
                print(f"Error processing row {i}: {e}")
                continue
                
        cv2.imshow('captured text', image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

def show_document_interactive(file_path, df):
    """
    Interactive document viewer with zoom and pan capabilities.
    Uses PyQt5 for superior user experience, falls back to OpenCV if unavailable.
    
    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results containing columns:
                              text, left, top, width, height, class, value_class
    
    Features:
        - Smooth mouse wheel zoom
        - Pan with middle mouse or Ctrl+drag
        - Zoom In/Out buttons
        - Reset View button
        - Toggle bounding boxes visibility
        - High-quality rendering
        - Responsive interface
    """
    if PYQT5_AVAILABLE:
        print("Opening PyQt5 interactive viewer...")
        print("Controls: Mouse wheel=zoom, Middle/Ctrl+drag=pan, Buttons=controls")
        show_document_qt(file_path, df)
    else:
        print("PyQt5 not available, using OpenCV fallback...")
        print("Note: Install PyQt5 for interactive zoom/pan features: pip install PyQt5")
        show_document(file_path, df)

def show_ocr_result_interactive(file_path, df):
    """
    Interactive OCR result viewer with zoom and pan capabilities.
    Uses PyQt5 for superior user experience, falls back to OpenCV if unavailable.
    
    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results containing columns:
                              text, left, top, width, height
    
    Features:
        - Same as show_document_interactive but without classification labels
    """
    if PYQT5_AVAILABLE:
        print("Opening PyQt5 interactive viewer...")
        print("Controls: Mouse wheel=zoom, Middle/Ctrl+drag=pan, Buttons=controls")
        show_ocr_result_qt(file_path, df)
    else:
        print("PyQt5 not available, using OpenCV fallback...")
        print("Note: Install PyQt5 for interactive zoom/pan features: pip install PyQt5")
        show_ocr_result(file_path, df)

# Aliases for backward compatibility
show_document_qt_interactive = show_document_interactive
show_ocr_result_qt_interactive = show_ocr_result_interactive

def test_new_preview():
    """Test the new preview system."""
    import sys
    sys.path.append('.')
    
    from OCR import ocr
    import utils
    
    print("🚀 Testing New Preview System")
    print("=" * 40)
    
    # Load and process data
    file_path = 'data/F3.pdf'
    
    print("Loading and processing document...")
    df = ocr.do(file_path)
    df = utils.merge_texts(df)
    utils.classify_batch_values(df)
    df = utils.clean_texts(df)
    
    print(f"Processed {len(df)} text elements")
    
    if PYQT5_AVAILABLE:
        print("\n✅ PyQt5 Available - Full Interactive Features:")
        print("   - Smooth mouse wheel zoom")
        print("   - Pan with middle mouse or Ctrl+drag")
        print("   - Zoom In/Out/Reset buttons")
        print("   - Toggle bounding boxes")
        print("   - High-quality rendering")
        print("   - Responsive interface")
    else:
        print("\n⚠️  PyQt5 Not Available - Limited Features:")
        print("   - Basic OpenCV display only")
        print("   - No zoom/pan capabilities")
        print("   - Install PyQt5 for full features: pip install PyQt5")
    
    print(f"\nOpening interactive viewer for: {file_path}")
    show_document_interactive(file_path, df)
    
    print("✅ Test completed!")

if __name__ == "__main__":
    test_new_preview()
