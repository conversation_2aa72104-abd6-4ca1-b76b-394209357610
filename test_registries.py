#!/usr/bin/env python3
"""
Test centr<PERSON><PERSON><PERSON><PERSON> pro value_class a result_class.
"""

import utils

def test_registries():
    """Test funkcí pro práci s číselníky."""
    
    print("🔢 Test centrálních č<PERSON>n<PERSON>")
    print("=" * 40)
    
    # Test VALUE_CLASS_REGISTRY
    print("\n1. VALUE_CLASS_REGISTRY (automatická klasifikace typů):")
    value_registry = utils.get_value_class_registry()
    for class_id, class_name in sorted(value_registry.items()):
        print(f"   {class_id}: {class_name}")
    
    # Test KEY_CLASS_REGISTRY
    print("\n2. KEY_CLASS_REGISTRY (klasifikace klíčů z Excel listů):")
    key_registry = utils.get_key_class_registry()
    for class_id, class_name in sorted(key_registry.items()):
        print(f"   {class_id}: {class_name}")

    # Test RESULT_CLASS_REGISTRY
    print("\n3. RESULT_CLASS_REGISTRY (manuální klasifikace kategorií):")
    result_registry = utils.get_result_class_registry()
    for class_id, class_name in sorted(result_registry.items()):
        print(f"   {class_id}: {class_name}")
    
    # Test funkcí pro získání názvů
    print("\n4. Test funkcí pro získání názvů:")

    # Test value_class názvů
    print("   value_class názvy:")
    for test_id in [0, 1, 3, 8, 99]:  # včetně neexistujícího ID
        name = utils.get_value_class_name(test_id)
        print(f"      ID {test_id}: {name}")

    # Test key_class názvů
    print("   key_class názvy:")
    for test_id in [0, 1, 5, 12, 99]:  # včetně neexistujícího ID
        name = utils.get_key_class_name(test_id)
        print(f"      ID {test_id}: {name}")

    # Test result_class názvů
    print("   result_class názvy:")
    for test_id in [0, 1, 5, 18, 99]:  # včetně neexistujícího ID
        name = utils.get_result_class_name(test_id)
        print(f"      ID {test_id}: {name}")
    
    # Test dostupných kategorií pro dropdown
    print("\n5. Dostupné kategorie pro dropdown:")

    # Result classes
    available_result_classes = utils.get_available_result_classes()
    print(f"   Result classes - celkem: {len(available_result_classes)}")
    print("   První 5 result kategorií:")
    for class_id, class_name in available_result_classes[:5]:
        print(f"      ({class_id}, '{class_name}')")

    # Key classes
    available_key_classes = utils.get_available_key_classes()
    print(f"   Key classes - celkem: {len(available_key_classes)}")
    print("   První 5 key kategorií:")
    for class_id, class_name in available_key_classes[:5]:
        print(f"      ({class_id}, '{class_name}')")

    # Test načítání z Excel souboru
    print("\n6. Test načítání mapování z Excel souboru:")
    mapping = utils.load_key_class_mapping_from_xlsx()
    if mapping:
        print(f"   Načteno {len(mapping)} kategorií z Excel souboru")
        print("   První 5 kategorií:")
        for i, (name, class_id) in enumerate(list(mapping.items())[:5]):
            print(f"      '{name}' → {class_id}")

    # Test aktualizace číselníku z Excel souboru
    print("\n7. Test aktualizace KEY_CLASS_REGISTRY z Excel souboru:")
    success = utils.update_key_class_registry_from_xlsx()
    if success:
        updated_registry = utils.get_key_class_registry()
        print(f"   Aktualizovaný číselník má {len(updated_registry)} položek")
        print("   První 5 položek:")
        for class_id, class_name in list(updated_registry.items())[:5]:
            print(f"      {class_id}: {class_name}")

    print("\n✅ Test číselníků dokončen!")

if __name__ == "__main__":
    test_registries()
