#!/usr/bin/env python3
"""
Jednoduchý test pouze pro zobrazení key_class popisků.
"""

import sys
from PyQt5.QtWidgets import QApplication, QGraphicsView, QGraphicsScene, QGraphicsTextItem
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor
import utils

def test_simple_labels():
    """Test pouze key_class popisků."""
    
    print("🏷️  Jednoduchý test key_class popisků")
    print("=" * 45)
    
    # Vytvoříme QApplication
    app = QApplication(sys.argv)
    
    # Vytvoříme scénu a view
    scene = QGraphicsScene()
    view = QGraphicsView(scene)
    view.setWindowTitle("Test key_class popisků")
    view.resize(800, 600)
    
    # Testovací data
    test_labels = [
        (1, "Faktura", 100, 100),
        (5, "Číslo faktury", 300, 100),
        (9, "Dodavatel", 500, 100),
        (20, "DIČ", 100, 200),
        (12, "Datum vystavení", 300, 200)
    ]
    
    print("Přidávám popisky do scény:")
    for key_class_id, expected_name, x, y in test_labels:
        # Získáme název z číselníku
        key_class_name = utils.get_key_class_name(key_class_id)
        print(f"   ID {key_class_id} -> '{key_class_name}' na pozici ({x}, {y})")
        
        # Vytvoříme textový prvek
        text_item = QGraphicsTextItem()
        text_item.setPos(x, y)
        text_item.setHtml(f'<div style="background-color: yellow; padding: 6px; border: 3px solid red; color: red; font-weight: bold; font-size: 16px; font-family: Arial;">{key_class_name}</div>')
        
        # Přidáme do scény
        scene.addItem(text_item)
    
    # Nastavíme velikost scény
    scene.setSceneRect(0, 0, 700, 400)
    
    print("\nZobrazuji okno s popisky...")
    print("💡 Měli byste vidět žluté popisky s červeným rámečkem!")
    
    # Zobrazíme view
    view.show()
    
    # Spustíme aplikaci
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_simple_labels()
