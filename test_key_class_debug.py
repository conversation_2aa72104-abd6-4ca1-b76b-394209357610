#!/usr/bin/env python3
"""
Debug test pro key_class zobrazování v preview.
"""

import pandas as pd
import sys
from PyQt5.QtWidgets import QApplication
from preview_qt import DocumentViewer
import utils

def test_key_class_debug():
    """Debug test pro key_class."""
    
    print("🔍 Debug test key_class zobrazování")
    print("=" * 45)
    
    # Vytvoříme testovací data s key_class hodnotami
    test_data = {
        'text': [
            'FAKTURA',               # key_class: 1 (Faktura)
            'F-2024-001',            # key_class: 5 (<PERSON><PERSON><PERSON> fak<PERSON>)  
            'Dodavatel s.r.o.',      # key_class: 9 (Dodavatel)
            'CZ12345678',            # key_class: 20 (DIČ)
            'Běžný text'             # key_class: 0 (Ostatní)
        ],
        'left': [50, 200, 350, 500, 50],
        'top': [50, 50, 50, 50, 150],
        'width': [120, 100, 150, 100, 100],
        'height': [25, 25, 25, 25, 25],
        'key_class': [1, 5, 9, 20, 0],  # Key class hodnoty
        'value_class': [0, 5, 0, 7, 0], # Value class pro porovnání
        'result_class': [0, 1, 0, 7, 0] # Result class pro dropdown
    }
    
    df = pd.DataFrame(test_data)
    
    print("1. Testovací data:")
    print(df[['text', 'key_class', 'value_class']])
    
    print("\n2. Ověření utils funkcí:")
    for i, row in df.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        print(f"   {row['text']} (key_class={row['key_class']}) -> '{key_name}'")
    
    print("\n3. Kontrola sloupců:")
    print(f"   Sloupce: {list(df.columns)}")
    print(f"   'key_class' in columns: {'key_class' in df.columns}")
    
    print("\n4. Simulace logiky preview_qt.py:")
    for i, row in df.iterrows():
        key_class = row['key_class'] if 'key_class' in df.columns else 0
        print(f"   text='{row['text']}', key_class={key_class}, type={type(key_class)}")
        if key_class > 0:
            key_class_name = utils.get_key_class_name(key_class)
            print(f"      -> Měl by se zobrazit červený popisek: '{key_class_name}'")
        else:
            print(f"      -> Žádný červený popisek (key_class = {key_class})")
    
    print("\n5. Spuštění DocumentViewer s debug výpisy...")
    
    # Vytvoříme QApplication
    app = QApplication(sys.argv)
    
    # Vytvoříme viewer
    viewer = DocumentViewer()
    
    # Vytvoříme dummy obrázek (bílé pozadí)
    import numpy as np
    dummy_image = np.ones((600, 800, 3), dtype=np.uint8) * 255  # Bílé pozadí
    
    # Načteme obrázek a bounding boxy
    viewer.load_image_from_array(dummy_image)
    
    print("   Volám add_bounding_boxes...")
    viewer.add_bounding_boxes(df)
    
    print("   Zobrazuji viewer...")
    viewer.show()
    viewer.resize(1000, 700)
    
    print("\n💡 Hledejte červené popisky nad bounding boxy!")
    print("   Očekávané červené popisky:")
    for i, row in df.iterrows():
        if row['key_class'] > 0:
            key_name = utils.get_key_class_name(row['key_class'])
            print(f"      '{row['text']}' -> '{key_name}'")
    
    # Spustíme aplikaci
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_key_class_debug()
