#!/usr/bin/env python3
"""
Test skript pro novou CSV export funkcionalitu.
Testuje export filtrovaných výsledků do složky Results.
"""

import sys
import os
sys.path.append('.')

import pandas as pd
import utils
from OCR import ocr

def create_test_data():
    """Vytvoří testovací data s r<PERSON>z<PERSON><PERSON><PERSON> k<PERSON> key_class a result_class."""
    test_data = {
        'text': [
            'FAKTURA',           # key_class=1, result_class=1 -> EXPORT
            '2024-01-15',        # key_class=0, result_class=2 -> NEEXPORT (key_class=0)
            'F2024001',          # key_class=5, result_class=1 -> EXPORT
            'Dodavatel s.r.o.',  # key_class=9, result_class=0 -> NEEXPORT (result_class=0)
            '31.12.2024',        # key_class=13, result_class=3 -> EXPORT
            'Běžný text',        # key_class=0, result_class=0 -> NEEXPORT (oba=0)
            'CZ12345678',        # key_class=20, result_class=7 -> EXPORT
            '123456789',         # key_class=6, result_class=6 -> EXPORT
            'Celkem k úhradě',   # key_class=15, result_class=17 -> EXPORT
            'Poznámka'           # key_class=0, result_class=0 -> NEEXPORT (oba=0)
        ],
        'left': [50, 200, 350, 500, 50, 200, 350, 500, 50, 200],
        'top': [50, 50, 50, 50, 150, 150, 150, 150, 250, 250],
        'width': [120, 100, 120, 150, 100, 120, 140, 130, 100, 150],
        'height': [25, 25, 25, 25, 25, 25, 25, 25, 25, 25],
        'key_class': [1, 0, 5, 9, 13, 0, 20, 6, 15, 0],
        'value_class': [0, 1, 5, 0, 1, 0, 7, 8, 3, 0],
        'result_class': [1, 2, 1, 0, 3, 0, 7, 6, 17, 0]
    }
    
    return pd.DataFrame(test_data)

def test_export_functions():
    """Testuje jednotlivé export funkce."""
    print("🧪 Test export funkcí")
    print("=" * 50)
    
    # Test 1: Zajištění složky Results
    print("\n1. Test zajištění složky Results:")
    results_dir = utils.ensure_results_directory()
    print(f"   ✓ Složka: {results_dir}")
    print(f"   ✓ Existuje: {os.path.exists(results_dir)}")
    
    # Test 2: Generování názvu souboru
    print("\n2. Test generování názvu souboru:")
    test_file_path = "data/F3.pdf"
    output_file = utils.get_output_filename(test_file_path)
    print(f"   ✓ Vstupní soubor: {test_file_path}")
    print(f"   ✓ Výstupní soubor: {output_file}")
    
    # Test 3: Získání rozměrů stránky
    print("\n3. Test získání rozměrů stránky:")
    if os.path.exists(test_file_path):
        try:
            width, height = utils.get_page_dimensions(test_file_path)
            print(f"   ✓ Rozměry stránky: {width} x {height} px")
        except Exception as e:
            print(f"   ❌ Chyba: {e}")
    else:
        print(f"   ⚠️  Soubor {test_file_path} neexistuje, používám výchozí rozměry")
        width, height = utils.get_page_dimensions("neexistujici.pdf")
        print(f"   ✓ Výchozí rozměry: {width} x {height} px")

def test_filtered_export():
    """Testuje filtrovaný export s testovacími daty."""
    print("\n🎯 Test filtrovaného exportu")
    print("=" * 50)
    
    # Vytvoříme testovací data
    df = create_test_data()
    
    print("\n1. Testovací data:")
    print("   Text                | key_class | result_class | Export?")
    print("   " + "-" * 60)
    for i, row in df.iterrows():
        export_status = "ANO" if (row['key_class'] > 0 and row['result_class'] > 0) else "NE"
        print(f"   {row['text']:<18} | {row['key_class']:>9} | {row['result_class']:>12} | {export_status}")
    
    # Spočítáme očekávané výsledky
    expected_filtered = len(df[(df['key_class'] > 0) & (df['result_class'] > 0)])
    print(f"\n2. Očekávané výsledky:")
    print(f"   Celkem řádků: {len(df)}")
    print(f"   Očekáváno k exportu: {expected_filtered}")
    print(f"   + 1 řádek 'page' = {expected_filtered + 1} celkem")
    
    # Test exportu
    print(f"\n3. Spouštím export...")
    try:
        # Použijeme dummy file_path pro test
        test_file_path = "test_document.pdf"
        output_file = utils.export_filtered_results(df, test_file_path)
        
        # Ověříme výsledek
        if os.path.exists(output_file):
            exported_df = pd.read_csv(output_file)
            print(f"\n4. Ověření exportu:")
            print(f"   ✓ Soubor vytvořen: {output_file}")
            print(f"   ✓ Exportováno řádků: {len(exported_df)}")
            print(f"   ✓ Očekáváno: {expected_filtered + 1}")
            
            # Zkontrolujeme řádek "page"
            page_rows = exported_df[exported_df['text'] == 'page']
            if len(page_rows) == 1:
                page_row = page_rows.iloc[0]
                print(f"   ✓ Řádek 'page' nalezen:")
                print(f"     - left: {page_row['left']}, top: {page_row['top']}")
                print(f"     - width: {page_row['width']}, height: {page_row['height']}")
                print(f"     - všechny třídy: {page_row['key_class']}, {page_row['value_class']}, {page_row['result_class']}")
            else:
                print(f"   ❌ Řádek 'page' nenalezen nebo je jich více!")
            
            # Zobrazíme ukázku exportovaných dat
            print(f"\n5. Ukázka exportovaných dat:")
            print("   Text                | key_class | result_class")
            print("   " + "-" * 45)
            for i, row in exported_df.iterrows():
                print(f"   {row['text']:<18} | {row['key_class']:>9} | {row['result_class']:>12}")
                
        else:
            print(f"   ❌ Soubor nebyl vytvořen!")
            
    except Exception as e:
        print(f"   ❌ Chyba při exportu: {e}")

def test_real_document():
    """Testuje export s reálným dokumentem."""
    print("\n📄 Test s reálným dokumentem")
    print("=" * 50)
    
    file_path = 'data/F3.pdf'
    
    if not os.path.exists(file_path):
        print(f"   ⚠️  Soubor {file_path} neexistuje, přeskakujem test")
        return
    
    print(f"\n1. Načítání dokumentu: {file_path}")
    try:
        # Načteme a zpracujeme dokument
        df = ocr.do(file_path)
        df = utils.merge_texts(df)
        utils.classify_batch_values(df)
        df = utils.clean_texts(df)
        
        # Přidáme dummy key_class a result_class pro test
        df['key_class'] = 0
        df['result_class'] = 0
        
        # Nastavíme nějaké testovací hodnoty
        if len(df) > 0:
            df.loc[0, 'key_class'] = 1
            df.loc[0, 'result_class'] = 1
        if len(df) > 1:
            df.loc[1, 'key_class'] = 5
            df.loc[1, 'result_class'] = 2
        
        print(f"   ✓ Načteno prvků: {len(df)}")
        print(f"   ✓ Prvky k exportu: {len(df[(df['key_class'] > 0) & (df['result_class'] > 0)])}")
        
        # Export
        print(f"\n2. Export...")
        output_file = utils.export_filtered_results(df, file_path)
        
        if os.path.exists(output_file):
            exported_df = pd.read_csv(output_file)
            print(f"   ✅ Export úspěšný!")
            print(f"   📁 Soubor: {output_file}")
            print(f"   📊 Řádků: {len(exported_df)}")
        else:
            print(f"   ❌ Export se nezdařil!")
            
    except Exception as e:
        print(f"   ❌ Chyba: {e}")

def main():
    """Hlavní funkce testu."""
    print("🚀 Test CSV Export Functionality")
    print("=" * 60)
    
    # Test jednotlivých funkcí
    test_export_functions()
    
    # Test filtrovaného exportu
    test_filtered_export()
    
    # Test s reálným dokumentem
    test_real_document()
    
    print(f"\n✅ Testy dokončeny!")
    print(f"📁 Zkontrolujte složku 'Results' pro exportované soubory")

if __name__ == "__main__":
    main()
