#!/usr/bin/env python3
"""
PyQt5-based document viewer with smooth zoom/pan and reliable bounding box overlays.
Superior alternative to matplotlib-based preview.
"""

import sys
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QGraphicsView, QGraphicsScene,
                             QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem,
                             QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel,
                             QComboBox, QGraphicsProxyWidget, QMenu)
from PyQt5.QtCore import Qt, QRectF, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage, QPen, QBrush, QColor, QFont, QTransform, QPainter
from pdf2image import convert_from_path
import cv2
import utils

class InteractiveBoundingBox(QGraphicsRectItem):
    """Interactive bounding box that can be right-clicked to select class."""
    
    def __init__(self, x, y, w, h, index, parent=None):
        super().__init__(x, y, w, h, parent)
        self.index = index
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsRectItem.ItemIsSelectable, True)
        
    def contextMenuEvent(self, event):
        """Show context menu on right click."""
        menu = QMenu()
        menu.addAction("Set Class", self.on_set_class)
        menu.exec_(event.screenPos())
        
    def on_set_class(self):
        """Open class selection dialog."""
        # This will be connected to the viewer's class selection method
        if hasattr(self.scene(), "parent_view") and hasattr(self.scene().parent_view, "show_class_selector"):
            self.scene().parent_view.show_class_selector(self.index)

class DocumentViewer(QGraphicsView):
    """Custom QGraphicsView with smooth zoom and pan capabilities."""
    
    def __init__(self):
        super().__init__()
        
        # Setup scene
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        self.scene.parent_view = self  # Reference to this view for callbacks
        
        # Přidáme DataFrame pro ukládání dat
        self.df = None

        # Dostupné třídy pro klasifikaci - načteme z centrálního číselníku
        self.available_classes = utils.get_available_result_classes()

        # Aktivní výběrové seznamy (pro správu jejich životního cyklu)
        self.active_selectors = []
        
        # Configure view
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHints(QPainter.Antialiasing | QPainter.SmoothPixmapTransform)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        
        # Zoom settings
        self.zoom_factor = 1.15
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        self.current_zoom = 1.0
        
        # Image and overlay items
        self.image_item = None
        self.bbox_items = []
        self.text_items = []
    
    def wheelEvent(self, event):
        """Handle mouse wheel zoom."""
        # Get zoom direction
        if event.angleDelta().y() > 0:
            zoom_in = True
        else:
            zoom_in = False
        
        # Calculate new zoom level
        if zoom_in:
            new_zoom = self.current_zoom * self.zoom_factor
        else:
            new_zoom = self.current_zoom / self.zoom_factor
        
        # Apply zoom limits
        new_zoom = max(self.min_zoom, min(self.max_zoom, new_zoom))
        
        if new_zoom != self.current_zoom:
            # Apply zoom
            scale_factor = new_zoom / self.current_zoom
            self.scale(scale_factor, scale_factor)
            self.current_zoom = new_zoom
    
    def mousePressEvent(self, event):
        """Handle mouse press for panning."""
        if event.button() == Qt.MiddleButton or (event.button() == Qt.LeftButton and event.modifiers() & Qt.ControlModifier):
            self.setDragMode(QGraphicsView.ScrollHandDrag)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release."""
        if event.button() == Qt.MiddleButton or (event.button() == Qt.LeftButton and event.modifiers() & Qt.ControlModifier):
            self.setDragMode(QGraphicsView.RubberBandDrag)
        super().mouseReleaseEvent(event)
    
    def load_image_from_array(self, image_array):
        """Load image from numpy array."""
        # Clear existing items
        self.clear_overlays()
        
        # Convert numpy array to QImage
        if len(image_array.shape) == 3:
            height, width, channel = image_array.shape
            bytes_per_line = 3 * width
            q_image = QImage(image_array.data, width, height, bytes_per_line, QImage.Format_RGB888)
        else:
            height, width = image_array.shape
            bytes_per_line = width
            q_image = QImage(image_array.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
        
        # Convert to pixmap and add to scene
        pixmap = QPixmap.fromImage(q_image)
        
        if self.image_item:
            self.scene.removeItem(self.image_item)
        
        self.image_item = QGraphicsPixmapItem(pixmap)
        self.scene.addItem(self.image_item)
        
        # Fit image in view
        self.fitInView(self.image_item, Qt.KeepAspectRatio)
        self.current_zoom = 1.0
    
    def add_bounding_boxes(self, df):
        """Add interactive bounding boxes and labels from DataFrame."""
        self.clear_overlays()
        self.df = df.copy()  # Store DataFrame for later updates

        # Inicializujeme result_class sloupec pokud neexistuje
        if 'result_class' not in self.df.columns:
            self.df['result_class'] = 0  # Výchozí hodnota 0 místo None

        for i in range(len(df)):
            text = df.loc[i, 'text']
            x = df.loc[i, 'left']
            y = df.loc[i, 'top']
            w = df.loc[i, 'width']
            h = df.loc[i, 'height']
            key_class = df.loc[i, 'key_class'] if 'key_class' in df.columns else 0
            value_class = df.loc[i, 'value_class'] if 'value_class' in df.columns else 0
            result_class = df.loc[i, 'result_class'] if 'result_class' in df.columns else 0

            # Create interactive bounding box
            rect_item = InteractiveBoundingBox(x, y, w, h, i)
            pen = QPen(QColor(0, 255, 0), 3)
            pen.setCosmetic(True)
            rect_item.setPen(pen)
            rect_item.setBrush(QBrush(QColor(0, 255, 0, 30)))
            self.scene.addItem(rect_item)
            self.bbox_items.append(rect_item)

            # Add key_class label (red, above box)
            if key_class is not None:
                key_text = QGraphicsTextItem(str(key_class))
                key_text.setPos(x, y - 25)
                key_text.setDefaultTextColor(QColor(255, 0, 0))  # Red
                font = QFont("Arial", 18, QFont.Bold)
                key_text.setFont(font)
                # Add background for better visibility
                key_text.setHtml(f'<div style="background-color: rgba(255,255,255,200); padding: 2px; border: 1px solid red;">{key_class}</div>')
                self.scene.addItem(key_text)
                self.text_items.append(key_text)

            # Add value_class label (blue, below box)
            if value_class > 0:
                value_class_name = utils.get_value_class_name(value_class)
                value_text = QGraphicsTextItem(value_class_name)
                value_text.setPos(x, y + h + 5)
                value_text.setDefaultTextColor(QColor(0, 0, 255))  # Blue
                font = QFont("Arial", 18, QFont.Bold)
                value_text.setFont(font)
                # Add background for better visibility
                value_text.setHtml(f'<div style="background-color: rgba(255,255,255,200); padding: 2px; border: 1px solid blue;">{value_class_name}</div>')
                self.scene.addItem(value_text)
                self.text_items.append(value_text)

                # Přidáme výběrový seznam vlevo od bounding boxu pro prvky s value_class > 0
                self.add_selector_for_element(i, x, y, w, h, result_class)
    
    def add_selector_for_element(self, index, x, y, w, h, current_result_class):
        """Přidá výběrový seznam vlevo od bounding boxu."""
        # Vytvoříme combo box s dostupnými třídami
        combo = QComboBox()
        combo.addItem("-- Vyberte --", 0)  # Výchozí hodnota 0

        # Přidáme všechny dostupné třídy (číslo, text)
        for class_id, class_text in self.available_classes:
            combo.addItem(class_text, class_id)

        # Nastavíme aktuální hodnotu pokud existuje
        if current_result_class is not None and current_result_class != 0:
            # Najdeme index podle hodnoty (class_id)
            for i in range(combo.count()):
                if combo.itemData(i) == current_result_class:
                    combo.setCurrentIndex(i)
                    break

        # Umístíme combo box vlevo od bounding boxu
        selector_x =  x + w + 10
        selector_y = y - 10

        # Vytvoříme proxy widget pro přidání do scény
        proxy = QGraphicsProxyWidget()
        proxy.setWidget(combo)
        proxy.setPos(selector_x, selector_y)
        self.scene.addItem(proxy)

        # Uložíme referenci pro pozdější vyčištění
        self.active_selectors.append(proxy)

        # Připojíme signál pro aktualizaci - používáme currentIndexChanged pro získání data
        combo.currentIndexChanged.connect(lambda idx: self.update_result_class(index, combo.itemData(idx)))

    def clear_overlays(self):
        """Clear all bounding boxes and text overlays."""
        for item in self.bbox_items:
            self.scene.removeItem(item)
        for item in self.text_items:
            self.scene.removeItem(item)
        for item in self.active_selectors:
            self.scene.removeItem(item)
        self.bbox_items.clear()
        self.text_items.clear()
        self.active_selectors.clear()
    
    def reset_view(self):
        """Reset zoom and center the image."""
        if self.image_item:
            self.fitInView(self.image_item, Qt.KeepAspectRatio)
            self.current_zoom = 1.0

    def update_result_class(self, index, class_id):
        """Aktualizuje result_class pro daný index v DataFrame."""
        if self.df is not None and index < len(self.df):
            # Aktualizujeme DataFrame - ukládáme číselnou hodnotu
            self.df.loc[index, 'result_class'] = class_id

            # Najdeme textový popis pro logování
            class_text = "-- Vyberte --"
            if class_id != 0:
                for cid, ctext in self.available_classes:
                    if cid == class_id:
                        class_text = ctext
                        break

            print(f"Aktualizován prvek {index}: result_class = {class_id} ({class_text})")

    def get_updated_dataframe(self):
        """Vrátí aktualizovaný DataFrame s result_class sloupcem."""
        return self.df.copy() if self.df is not None else None

    def show_class_selector(self, index):
        """Show class selector for the given bounding box index (legacy method for compatibility)."""
        # Tato metoda je zachována pro kompatibilitu s InteractiveBoundingBox
        # ale nyní používáme stálé výběrové seznamy
        pass

class DocumentViewerWindow(QMainWindow):
    """Main window for document viewing."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Document Viewer - OCR Results")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create toolbar
        toolbar = QHBoxLayout()

        self.zoom_label = QLabel("Zoom: 100%")
        toolbar.addWidget(self.zoom_label)

        # Zoom buttons
        zoom_in_btn = QPushButton("Zoom In (+)")
        zoom_in_btn.clicked.connect(self.zoom_in)
        toolbar.addWidget(zoom_in_btn)

        zoom_out_btn = QPushButton("Zoom Out (-)")
        zoom_out_btn.clicked.connect(self.zoom_out)
        toolbar.addWidget(zoom_out_btn)

        reset_btn = QPushButton("Reset View")
        reset_btn.clicked.connect(self.reset_view)
        toolbar.addWidget(reset_btn)

        # Toggle bounding boxes
        self.toggle_bbox_btn = QPushButton("Hide Boxes")
        self.toggle_bbox_btn.clicked.connect(self.toggle_bounding_boxes)
        toolbar.addWidget(self.toggle_bbox_btn)

        toolbar.addStretch()

        info_label = QLabel("Wheel: zoom | Middle/Ctrl+drag: pan | Buttons: controls")
        toolbar.addWidget(info_label)
        
        layout.addLayout(toolbar)
        
        # Create viewer
        self.viewer = DocumentViewer()
        layout.addWidget(self.viewer)

        # Connect zoom updates
        self.viewer.scene.changed.connect(self.update_zoom_label)

        # Přidáme tlačítko pro export výsledků
        export_btn = QPushButton("Export Results")
        export_btn.clicked.connect(self.export_results)
        toolbar.addWidget(export_btn)
    
    def load_document(self, file_path, df):
        """Load document and display with bounding boxes."""
        # Convert PDF to image
        images = convert_from_path(file_path, dpi=300, output_folder=None)
        
        if images:
            # Convert PIL image to numpy array
            image = np.array(images[0])
            
            # Load image
            self.viewer.load_image_from_array(image)
            
            # Add bounding boxes
            self.viewer.add_bounding_boxes(df)
            
            self.setWindowTitle(f"Document Viewer - {file_path}")
    
    def zoom_in(self):
        """Zoom in programmatically."""
        new_zoom = self.viewer.current_zoom * self.viewer.zoom_factor
        new_zoom = min(self.viewer.max_zoom, new_zoom)
        if new_zoom != self.viewer.current_zoom:
            scale_factor = new_zoom / self.viewer.current_zoom
            self.viewer.scale(scale_factor, scale_factor)
            self.viewer.current_zoom = new_zoom
            self.update_zoom_label()

    def zoom_out(self):
        """Zoom out programmatically."""
        new_zoom = self.viewer.current_zoom / self.viewer.zoom_factor
        new_zoom = max(self.viewer.min_zoom, new_zoom)
        if new_zoom != self.viewer.current_zoom:
            scale_factor = new_zoom / self.viewer.current_zoom
            self.viewer.scale(scale_factor, scale_factor)
            self.viewer.current_zoom = new_zoom
            self.update_zoom_label()

    def reset_view(self):
        """Reset view to fit image."""
        self.viewer.reset_view()
        self.update_zoom_label()

    def toggle_bounding_boxes(self):
        """Toggle visibility of bounding boxes."""
        visible = len(self.viewer.bbox_items) > 0 and self.viewer.bbox_items[0].isVisible()

        for item in self.viewer.bbox_items + self.viewer.text_items:
            item.setVisible(not visible)

        self.toggle_bbox_btn.setText("Show Boxes" if visible else "Hide Boxes")

    def update_zoom_label(self):
        """Update zoom percentage in toolbar."""
        zoom_percent = int(self.viewer.current_zoom * 100)
        self.zoom_label.setText(f"Zoom: {zoom_percent}%")

    def export_results(self):
        """Export aktualizovaného DataFrame s result_class."""
        df = self.viewer.get_updated_dataframe()
        if df is not None:
            # Zobrazíme statistiky
            total_elements = len(df)
            classified_elements = len(df[df['result_class'] > 0])  # Změna: > 0 místo notna()

            print(f"\n=== Export Results ===")
            print(f"Celkem prvků: {total_elements}")
            print(f"Klasifikovaných prvků: {classified_elements}")
            print(f"Neklasifikovaných prvků: {total_elements - classified_elements}")

            # Zobrazíme rozložení podle result_class
            if classified_elements > 0:
                print(f"\nRozložení podle result_class:")
                result_counts = df[df['result_class'] > 0]['result_class'].value_counts().sort_index()

                # Použijeme centrální číselník pro zobrazení textových popisů
                class_mapping = utils.get_result_class_registry()

                for class_id, count in result_counts.items():
                    class_text = class_mapping.get(class_id, f"Neznámá třída {class_id}")
                    print(f"  {class_id} ({class_text}): {count}")

            # Zde můžeme přidat uložení do souboru
            # df.to_csv('results.csv', index=False)
            print(f"\nDataFrame je dostupný pro další zpracování.")
            return df
        else:
            print("Žádná data k exportu.")

def show_document_qt(file_path, df, return_results=False):
    """
    Show document with OCR results using PyQt5 viewer.

    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results
        return_results (bool): If True, returns updated DataFrame after user interaction

    Returns:
        pandas.DataFrame or None: Updated DataFrame with result_class column if return_results=True
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    viewer = DocumentViewerWindow()
    viewer.load_document(file_path, df)
    viewer.show()

    app.exec_()

    # Vrátíme aktualizovaný DataFrame pokud je požadován
    if return_results:
        return viewer.viewer.get_updated_dataframe()
    return None

def show_ocr_result_qt(file_path, df, return_results=False):
    """
    Show OCR results using PyQt5 viewer (same as show_document_qt for consistency).

    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results
        return_results (bool): If True, returns updated DataFrame after user interaction

    Returns:
        pandas.DataFrame or None: Updated DataFrame with result_class column if return_results=True
    """
    return show_document_qt(file_path, df, return_results)

if __name__ == "__main__":
    # Test the viewer
    import sys
    sys.path.append('.')
    
    from OCR import ocr
    import utils
    
    print("Testing PyQt5 Document Viewer...")
    
    # Load and process data
    df = ocr.do('data/F3.pdf')
    df = utils.merge_texts(df)
    utils.classify_batch_values(df)
    df = utils.clean_texts(df)
    
    print(f"Loaded {len(df)} text elements")
    print("Opening PyQt5 viewer...")
    
    show_document_qt('data/F3.pdf', df)
