#!/usr/bin/env python3
"""
Test zobrazování n<PERSON>zvů místo čísel v preview.
"""

import pandas as pd
import utils

def test_preview_names():
    """Test zobrazování názvů v preview."""
    
    print("📋 Test zobrazování názvů v preview")
    print("=" * 45)
    
    # Vytvoříme testovací data
    test_data = {
        'text': ['25.12.2023', '123456', '15.5%', 'CZ12345678', 'ABC123'],
        'left': [100, 200, 300, 400, 500],
        'top': [100, 100, 100, 100, 100],
        'width': [80, 60, 40, 80, 60],
        'height': [20, 20, 20, 20, 20],
        'class': [None, None, None, None, None],
        'value_class': [1, 8, 2, 7, 5],  # <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Procenta, DIČ, Alfanumerický
        'result_class': [2, 1, 14, 7, 5]  # <PERSON><PERSON> v<PERSON>, <PERSON><PERSON><PERSON> f<PERSON>, <PERSON>z<PERSON> DPH, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
    }
    
    df = pd.DataFrame(test_data)
    
    print("1. Testovací data:")
    for i, row in df.iterrows():
        print(f"   '{row['text']}' - value_class: {row['value_class']}, result_class: {row['result_class']}")
    
    print("\n2. Zobrazení value_class názvů (modré popisky):")
    for i, row in df.iterrows():
        value_name = utils.get_value_class_name(row['value_class'])
        print(f"   '{row['text']}' → {value_name}")
    
    print("\n3. Zobrazení result_class názvů (dropdown výběr):")
    for i, row in df.iterrows():
        result_name = utils.get_result_class_name(row['result_class'])
        print(f"   '{row['text']}' → {result_name}")
    
    print("\n4. Dostupné kategorie pro dropdown:")
    available = utils.get_available_result_classes()
    print(f"   Celkem kategorií: {len(available)}")
    print("   Ukázka prvních 8:")
    for class_id, class_name in available[:8]:
        print(f"      {class_id}: {class_name}")
    
    print("\n5. Test neexistujících ID:")
    print(f"   value_class 99: {utils.get_value_class_name(99)}")
    print(f"   result_class 99: {utils.get_result_class_name(99)}")
    
    print("\n✅ Test zobrazování názvů dokončen!")
    print("💡 V preview se nyní zobrazují názvy místo čísel")

if __name__ == "__main__":
    test_preview_names()
