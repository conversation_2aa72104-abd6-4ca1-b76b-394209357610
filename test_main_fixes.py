#!/usr/bin/env python3
"""
Test oprav v main.py - kontrola key_class klasifikace a centrálních číselníků.
"""

import pandas as pd
import utils
import os

def test_main_fixes():
    """Test oprav v main.py."""
    
    print("🔧 Test oprav v main.py")
    print("=" * 40)
    
    # Test 1: Kontrola načítání mapování z Excel
    print("1. Test načítání mapování kategorií z Excel:")
    mapping = utils.load_key_class_mapping_from_xlsx()
    if mapping:
        print(f"   ✓ Načteno {len(mapping)} kategorií z Excel souboru")
        print("   Ukázka mapování:")
        for i, (name, class_id) in enumerate(list(mapping.items())[:5]):
            print(f"      '{name}' → {class_id}")
    else:
        print("   ❌ Nepodařilo se načíst mapování z Excel souboru")
        print("   ⚠️  Zkontrolujte, zda existuje soubor training_data/training_set.xlsx")
    
    # Test 2: Kontrola centrálních číselníků
    print("\n2. Test centrálních číselníků:")
    
    # Test result_class číselníku
    print("   Result class číselník:")
    for test_id in [0, 1, 5, 18]:
        name = utils.get_result_class_name(test_id)
        print(f"      ID {test_id}: {name}")
    
    # Test key_class číselníku
    print("   Key class číselník:")
    for test_id in [0, 1, 5, 20]:
        name = utils.get_key_class_name(test_id)
        print(f"      ID {test_id}: {name}")
    
    # Test value_class číselníku
    print("   Value class číselník:")
    for test_id in [0, 1, 3, 8]:
        name = utils.get_value_class_name(test_id)
        print(f"      ID {test_id}: {name}")
    
    # Test 3: Simulace výsledků main.py
    print("\n3. Simulace výsledků main.py s opravami:")
    
    # Vytvoříme testovací DataFrame s všemi třemi typy klasifikace
    test_data = {
        'text': [
            'FAKTURA',           # key_class: 1, value_class: 0, result_class: 0
            'F-2024-001',        # key_class: 5, value_class: 5, result_class: 1
            '25.12.2023',        # key_class: 12, value_class: 1, result_class: 2
            'CZ12345678',        # key_class: 20, value_class: 7, result_class: 7
            '15.5%',             # key_class: 0, value_class: 2, result_class: 14
            'Běžný text'         # key_class: 0, value_class: 0, result_class: 0
        ],
        'left': [100] * 6,
        'top': [100] * 6,
        'width': [80] * 6,
        'height': [20] * 6,
        'key_class': [1, 5, 12, 20, 0, 0],
        'value_class': [0, 5, 1, 7, 2, 0],
        'result_class': [0, 1, 2, 7, 14, 0],
        'similarity': [0.0, 0.92, 0.88, 0.95, 0.0, 0.0]
    }
    
    df = pd.DataFrame(test_data)
    
    print("   Testovací data:")
    print("   Text          | key_class | value_class | result_class | similarity")
    print("   " + "-" * 70)
    for i, row in df.iterrows():
        print(f"   {row['text']:<12} | {row['key_class']:>9} | {row['value_class']:>11} | {row['result_class']:>12} | {row['similarity']:>10.2f}")
    
    # Simulace statistik z main.py
    total_elements = len(df)
    result_classified = len(df[df['result_class'] > 0])
    key_classified = len(df[df['key_class'] > 0])
    
    print(f"\n   📊 Statistiky (simulace main.py výstupu):")
    print(f"      ✓ Total elements: {total_elements}")
    print(f"      ✓ Result classified elements: {result_classified}")
    print(f"      ✓ Result unclassified elements: {total_elements - result_classified}")
    print(f"      ✓ Key classified elements: {key_classified}")
    print(f"      ✓ Key unclassified elements: {total_elements - key_classified}")
    
    # Result class breakdown
    if result_classified > 0:
        print(f"\n      📋 Result class breakdown:")
        result_counts = df[df['result_class'] > 0]['result_class'].value_counts().sort_index()
        for class_id, count in result_counts.items():
            class_text = utils.get_result_class_name(class_id)
            print(f"         {class_id} ({class_text}): {count} elements")
    
    # Key class breakdown
    if key_classified > 0:
        print(f"\n      🔑 Key class breakdown:")
        key_counts = df[df['key_class'] > 0]['key_class'].value_counts().sort_index()
        for class_id, count in key_counts.items():
            class_text = utils.get_key_class_name(class_id)
            print(f"         {class_id} ({class_text}): {count} elements")
    
    # Sample of classified elements
    classified_sample = df[df['result_class'] > 0][['text', 'value_class', 'key_class', 'result_class']].head()
    if not classified_sample.empty:
        print(f"\n      🔍 Sample of classified elements:")
        for _, row in classified_sample.iterrows():
            value_class_name = utils.get_value_class_name(row['value_class'])
            key_class_name = utils.get_key_class_name(row['key_class'])
            result_class_name = utils.get_result_class_name(row['result_class'])
            print(f"         '{row['text']}' → value:{value_class_name}, key:{key_class_name}, result:{result_class_name}")
    
    # Test 4: Kontrola existence modelu
    print("\n4. Kontrola existence natrénovaného modelu:")
    model_path = 'model_paraphrase-multilingual-mpnet-base-v2'
    if os.path.exists(model_path):
        print(f"   ✓ Model nalezen: {model_path}")
        
        # Kontrola souborů v modelu
        model_files = os.listdir(model_path)
        important_files = ['centroids.pkl', 'config.json', 'pytorch_model.bin']
        
        print("   Soubory v modelu:")
        for file in important_files:
            if file in model_files:
                print(f"      ✓ {file}")
            else:
                print(f"      ❌ {file} (chybí)")
    else:
        print(f"   ❌ Model nenalezen: {model_path}")
        print("   ⚠️  Spusťte fine-tuning pro vytvoření modelu")
    
    print("\n5. Shrnutí oprav v main.py:")
    print("   ✅ Přidána kontrola načtení mapování z Excel souboru")
    print("   ✅ Přidáno explicitní nastavení mapování pro Classifier")
    print("   ✅ Přidána kontrola úspěšnosti načtení modelu")
    print("   ✅ Nahrazeno staré mapování centrálními číselníky")
    print("   ✅ Přidány statistiky key_class klasifikace")
    print("   ✅ Vylepšené zobrazení ukázkových prvků")
    
    print("\n✅ Test oprav v main.py dokončen!")
    print("\n💡 Nyní by main.py měl správně:")
    print("   - Načíst mapování kategorií z Excel souboru")
    print("   - Provést key_class klasifikaci s natrénovaným modelem")
    print("   - Zobrazit červené popisky key_class v preview")
    print("   - Používat centrální číselníky pro všechny typy klasifikace")

if __name__ == "__main__":
    test_main_fixes()
