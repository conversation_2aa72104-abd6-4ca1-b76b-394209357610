#!/usr/bin/env python3
"""
Test integrace číseln<PERSON>ů s Classifier třídou.
"""

import utils
from Classifier import Classifier

def test_classifier_integration():
    """Test integrace číselníků s Classifier třídou."""
    
    print("🔗 Test integrace číselníků s Classifier")
    print("=" * 50)
    
    # Test načítání mapování z Excel souboru
    print("1. Načítání mapování kategorií klíčů z Excel souboru:")
    mapping = utils.load_key_class_mapping_from_xlsx()
    
    if mapping:
        print(f"   ✓ Načteno {len(mapping)} kategorií")
        print("   Ukázka mapování:")
        for i, (name, class_id) in enumerate(list(mapping.items())[:5]):
            print(f"      '{name}' → {class_id}")
    else:
        print("   ❌ Nepodařilo se načíst mapování")
        return
    
    # Test vytvoření Classifier instance (bez skutečného modelu)
    print("\n2. Test nastavení mapování v Classifier:")
    try:
        # Vytvoříme dummy classifier pro test (bez načítání modelu)
        print("   Vytváření Classifier instance...")
        # Poznámka: Toto by normálně vyžadovalo skutečný model
        print("   (Přeskakuji vytvoření Classifier instance - vyžaduje model)")
        
        # Místo toho ukážeme, jak by se mapování použilo
        print("   Ukázka použití mapování:")
        print("   classifier.set_category_id_mapping(mapping)")
        
        # Simulujeme výsledky klasifikace
        test_categories = ["Faktura", "Číslo faktury", "Datum vystavení", "DIČ", "Neznámá kategorie"]
        
        print("\n   Simulace klasifikace s mapováním:")
        for category in test_categories:
            class_id = mapping.get(category, 0)  # 0 pro neznámé kategorie
            class_name = utils.get_key_class_name(class_id)
            print(f"      '{category}' → ID {class_id} → '{class_name}'")
            
    except Exception as e:
        print(f"   ❌ Chyba při testu Classifier: {e}")
    
    # Test porovnání s aktuálním výchozím mapováním v Classifier
    print("\n3. Porovnání s výchozím mapováním v Classifier:")
    
    # Výchozí mapování z Classifier.py (řádky 64-83)
    default_classifier_mapping = {
        "Číslo faktury": 1,
        "Datum vystaven": 2,
        "Datum splatnosti": 3,
        "DUZP": 4,
        "Číslo objednávky": 5,
        "Variabilní symbol": 6,
        "DIČ plátce": 7,
        "IČO plátce": 8,
        "DIČ dodavatele": 9,
        "IČO dodavatele": 10,
        "IBAN": 11,
        "Číslo účtu": 12,
        "Kód banky": 13,
        "Sazba DPH": 14,
        "Základ DPH": 15,
        "Částka celkem s DPH": 16,
        "Celková částka k úhradě": 17,
        "Měna": 18
    }
    
    print(f"   Výchozí mapování v Classifier: {len(default_classifier_mapping)} kategorií")
    print(f"   Mapování z Excel souboru: {len(mapping)} kategorií")
    
    # Najdeme společné kategorie
    common_categories = set(default_classifier_mapping.keys()) & set(mapping.keys())
    excel_only = set(mapping.keys()) - set(default_classifier_mapping.keys())
    classifier_only = set(default_classifier_mapping.keys()) - set(mapping.keys())
    
    print(f"   Společné kategorie: {len(common_categories)}")
    if common_categories:
        print("   Ukázka společných kategorií:")
        for category in list(common_categories)[:3]:
            print(f"      '{category}': Classifier={default_classifier_mapping[category]}, Excel={mapping[category]}")
    
    print(f"   Pouze v Excel: {len(excel_only)}")
    if excel_only:
        print("   Ukázka kategorií pouze v Excel:")
        for category in list(excel_only)[:3]:
            print(f"      '{category}' → {mapping[category]}")
    
    print(f"   Pouze v Classifier: {len(classifier_only)}")
    if classifier_only:
        print("   Ukázka kategorií pouze v Classifier:")
        for category in list(classifier_only)[:3]:
            print(f"      '{category}' → {default_classifier_mapping[category]}")
    
    # Doporučení
    print("\n4. Doporučení pro integraci:")
    print("   ✓ Použijte utils.load_key_class_mapping_from_xlsx() pro načtení aktuálního mapování")
    print("   ✓ Nastavte mapování v Classifier pomocí classifier.set_category_id_mapping(mapping)")
    print("   ✓ Použijte utils.get_key_class_name(id) pro zobrazení názvů kategorií")
    print("   ✓ Excel soubor je autoritativní zdroj pro kategorie klíčů")
    
    print("\n✅ Test integrace dokončen!")

if __name__ == "__main__":
    test_classifier_integration()
