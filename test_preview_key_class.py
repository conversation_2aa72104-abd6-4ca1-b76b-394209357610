#!/usr/bin/env python3
"""
Test zobrazování key_class v preview_qt.py.
"""

import pandas as pd
import utils

def test_preview_key_class():
    """Test zobrazování key_class v preview."""
    
    print("🔑 Test zobrazování key_class v preview")
    print("=" * 50)
    
    # Vytvoříme testovací DataFrame s key_class hodnotami
    test_data = {
        'text': [
            'FAKTURA č. 2024001',     # key_class: 1 (Faktura)
            '25.12.2023',             # key_class: 0 (Ostatní) - datum
            'F-2024-001',             # key_class: 5 (<PERSON><PERSON><PERSON>)
            'Dodavatel s.r.o.',       # key_class: 9 (Dodavatel)
            'CZ12345678',             # key_class: 20 (DIČ)
            'Datum vystavení:',       # key_class: 12 (<PERSON><PERSON> vys<PERSON>)
            'VS: 123456',             # key_class: 6 (<PERSON><PERSON><PERSON><PERSON><PERSON> symbol)
            '<PERSON><PERSON><PERSON> k <PERSON>',        # key_class: 15 (<PERSON><PERSON><PERSON>)
            'Běžný text',             # key_class: 0 (Ostatní)
            'IBAN: CZ65...'           # key_class: 21 (IBAN)
        ],
        'left': [100, 200, 300, 400, 500, 100, 200, 300, 400, 500],
        'top': [100, 100, 100, 100, 100, 200, 200, 200, 200, 200],
        'width': [120, 80, 100, 140, 90, 110, 90, 120, 80, 100],
        'height': [20, 20, 20, 20, 20, 20, 20, 20, 20, 20],
        'class': [None] * 10,  # Starý sloupec (nepoužívá se)
        'key_class': [1, 0, 5, 9, 20, 12, 6, 15, 0, 21],  # Nový sloupec s key_class
        'value_class': [0, 1, 5, 0, 7, 0, 8, 0, 0, 6],    # value_class pro porovnání
        'result_class': [1, 2, 1, 0, 7, 2, 6, 17, 0, 11]  # result_class pro porovnání
    }
    
    df = pd.DataFrame(test_data)
    
    print("1. Testovací data s key_class:")
    print("   Text                    | key_class | value_class | result_class")
    print("   " + "-" * 70)
    for i, row in df.iterrows():
        print(f"   {row['text']:<22} | {row['key_class']:>9} | {row['value_class']:>11} | {row['result_class']:>12}")
    
    print("\n2. Zobrazení key_class názvů (červené popisky nad bounding boxy):")
    print("   Text                    | key_class | Název kategorie")
    print("   " + "-" * 65)
    
    for i, row in df.iterrows():
        key_class_name = utils.get_key_class_name(row['key_class'])
        display_status = "ZOBRAZÍ SE" if row['key_class'] > 0 else "nezobrazí se"
        print(f"   {row['text']:<22} | {row['key_class']:>9} | {key_class_name} ({display_status})")
    
    print("\n3. Porovnání všech tří typů klasifikace:")
    print("   Text                    | Key (červená)        | Value (modrá)        | Result (dropdown)")
    print("   " + "-" * 95)
    
    for i, row in df.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        value_name = utils.get_value_class_name(row['value_class'])
        result_name = utils.get_result_class_name(row['result_class'])
        
        # Zkrátíme názvy pro lepší zobrazení
        key_display = key_name[:18] + "..." if len(key_name) > 18 else key_name
        value_display = value_name[:18] + "..." if len(value_name) > 18 else value_name
        result_display = result_name[:18] + "..." if len(result_name) > 18 else result_name
        
        print(f"   {row['text']:<22} | {key_display:<20} | {value_display:<20} | {result_display}")
    
    print("\n4. Statistiky key_class:")
    key_counts = df['key_class'].value_counts().sort_index()
    print(f"   Celkem prvků: {len(df)}")
    print(f"   Prvky s key_class > 0: {len(df[df['key_class'] > 0])}")
    print(f"   Prvky s key_class = 0: {len(df[df['key_class'] == 0])}")
    
    print("\n   Rozložení key_class kategorií:")
    for class_id, count in key_counts.items():
        class_name = utils.get_key_class_name(class_id)
        display_info = "červený popisek" if class_id > 0 else "bez popisku"
        print(f"      {class_id}: {class_name} - {count} prvků ({display_info})")
    
    print("\n5. Kontrola sloupců v DataFrame:")
    print(f"   Sloupce v DataFrame: {list(df.columns)}")
    print(f"   Má sloupec 'key_class': {'key_class' in df.columns}")
    print(f"   Má sloupec 'class': {'class' in df.columns}")
    print(f"   Má sloupec 'value_class': {'value_class' in df.columns}")
    print(f"   Má sloupec 'result_class': {'result_class' in df.columns}")
    
    print("\n6. Simulace logiky preview_qt.py:")
    print("   Původní logika (CHYBNÁ):")
    print("      key_class = df.loc[i, 'class'] if 'class' in df.columns else None")
    print("      if key_class is not None:")
    print("         # Zobrazí str(key_class)")
    
    print("\n   Opravená logika:")
    print("      key_class = df.loc[i, 'key_class'] if 'key_class' in df.columns else 0")
    print("      if key_class > 0:")
    print("         key_class_name = utils.get_key_class_name(key_class)")
    print("         # Zobrazí key_class_name červeně nad bounding boxem")
    
    print("\n7. Očekávané zobrazení v preview:")
    elements_with_key_class = df[df['key_class'] > 0]
    print(f"   Červené popisky se zobrazí u {len(elements_with_key_class)} prvků:")
    for i, row in elements_with_key_class.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        print(f"      '{row['text']}' → '{key_name}' (červeně nad bounding boxem)")
    
    print("\n✅ Test key_class v preview dokončen!")
    print("\n💡 Opravy v preview_qt.py:")
    print("   1. Změněno: key_class = df.loc[i, 'class'] → df.loc[i, 'key_class']")
    print("   2. Změněno: if key_class is not None → if key_class > 0")
    print("   3. Přidáno: key_class_name = utils.get_key_class_name(key_class)")
    print("   4. Změněno: zobrazení str(key_class) → key_class_name")

if __name__ == "__main__":
    test_preview_key_class()
