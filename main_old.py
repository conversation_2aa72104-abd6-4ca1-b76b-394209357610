# Inicializace OCR procesoru
import preview
import utils
from Classifier import Classifier
from OCR.OCRProcessor import OCRProcessor

ocr = OCRProcessor(language='ces', dpi=300)
file_path = 'data/F3.pdf'

# Načtení dokumentu
ocr.load_pdf(file_path)  # nebo ocr.load_image('obrazek.png')

df = ocr.get_items()
#df = ocr.extract_structured_data()

df = utils.merge_texts(df)

utils.classify_batch_values(df)
df = utils.clean_texts(df)
# Inicializace klasifikátoru
classifier = Classifier(model_name='paraphrase-multilingual-mpnet-base-v2')
classifier.load_model('model_paraphrase-multilingual-mpnet-base-v2')

# Klasifikace textů
df = classifier.batch_classify(
    df=df,
    text_column='text',
    class_column='key_class',
    similarity_column='similarity',
    threshold=0.85
)
preview.show_document(file_path, df)
# preview.show_ocr_result(file_path, df)