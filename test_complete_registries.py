#!/usr/bin/env python3
"""
Kompletní test všech tř<PERSON> a jejich p<PERSON>.
"""

import pandas as pd
import utils

def test_complete_registries():
    """Test kompletního použití všech tř<PERSON>."""
    
    print("🎯 Kompletní test všech číselníků")
    print("=" * 50)
    
    # Vytvoříme testovací DataFrame s všemi třemi typy klasifikace
    test_data = {
        'text': [
            'FAKTURA',           # key_class
            '25.12.2023',        # value_class: datum
            '123456',            # value_class: číslo
            'CZ12345678',        # value_class: DIČ
            '15.5%',             # value_class: procenta
            '<PERSON><PERSON><PERSON> faktury',     # key_class
            'Datum vystavení',   # key_class
            'ABC123',            # value_class: alfanume<PERSON>ý
            'IBAN',              # key_class
            '1234.56'            # value_class: desetinn<PERSON>
        ],
        'left': [100] * 10,
        'top': [100] * 10,
        'width': [80] * 10,
        'height': [20] * 10,
        'class': [None] * 10,
        'value_class': [0, 1, 8, 7, 2, 0, 0, 5, 0, 3],  # <PERSON>k<PERSON> klasifikace typů
        'key_class': [1, 0, 0, 0, 0, 5, 12, 0, 21, 0],   # <PERSON>lasifikace kl<PERSON><PERSON><PERSON> (z Excel)
        'result_class': [0, 2, 1, 7, 14, 1, 2, 5, 11, 17]  # <PERSON>u<PERSON>ln<PERSON> klasifikace
    }
    
    df = pd.DataFrame(test_data)
    
    print("1. Testovací data:")
    print("   Text                | value_class | key_class | result_class")
    print("   " + "-" * 60)
    for i, row in df.iterrows():
        print(f"   {row['text']:<18} | {row['value_class']:>11} | {row['key_class']:>9} | {row['result_class']:>12}")
    
    print("\n2. Zobrazení s názvy kategorií:")
    print("   Text                | Value Type        | Key Category      | Result Category")
    print("   " + "-" * 80)
    
    for i, row in df.iterrows():
        value_name = utils.get_value_class_name(row['value_class'])
        key_name = utils.get_key_class_name(row['key_class'])
        result_name = utils.get_result_class_name(row['result_class'])
        
        print(f"   {row['text']:<18} | {value_name:<17} | {key_name:<17} | {result_name}")
    
    print("\n3. Statistiky podle kategorií:")
    
    # Value class statistiky
    print("\n   📊 Value Class (automatická klasifikace typů):")
    value_counts = df['value_class'].value_counts().sort_index()
    for class_id, count in value_counts.items():
        if class_id > 0:  # Přeskočíme "Neznámý typ"
            class_name = utils.get_value_class_name(class_id)
            print(f"      {class_id}: {class_name} - {count} prvků")
    
    # Key class statistiky
    print("\n   🔑 Key Class (klasifikace klíčů z Excel):")
    key_counts = df['key_class'].value_counts().sort_index()
    for class_id, count in key_counts.items():
        if class_id > 0:  # Přeskočíme "Ostatní"
            class_name = utils.get_key_class_name(class_id)
            print(f"      {class_id}: {class_name} - {count} prvků")
    
    # Result class statistiky
    print("\n   🎯 Result Class (manuální klasifikace kategorií):")
    result_counts = df['result_class'].value_counts().sort_index()
    for class_id, count in result_counts.items():
        if class_id > 0:  # Přeskočíme "Ostatní"
            class_name = utils.get_result_class_name(class_id)
            print(f"      {class_id}: {class_name} - {count} prvků")
    
    print("\n4. Test dostupných kategorií pro dropdown:")
    
    # Key classes pro Classifier
    key_classes = utils.get_available_key_classes()
    print(f"   🔑 Key classes (pro Classifier): {len(key_classes)} kategorií")
    print("      První 5 kategorií:")
    for class_id, class_name in key_classes[:5]:
        print(f"         {class_id}: {class_name}")
    
    # Result classes pro preview
    result_classes = utils.get_available_result_classes()
    print(f"   🎯 Result classes (pro preview): {len(result_classes)} kategorií")
    print("      První 5 kategorií:")
    for class_id, class_name in result_classes[:5]:
        print(f"         {class_id}: {class_name}")
    
    print("\n5. Test integrace s Excel souborem:")
    
    # Načtení mapování z Excel
    excel_mapping = utils.load_key_class_mapping_from_xlsx()
    if excel_mapping:
        print(f"   ✓ Načteno {len(excel_mapping)} kategorií z Excel souboru")
        
        # Ukázka použití s testovacími daty
        print("   Simulace klasifikace s Excel mapováním:")
        test_texts = ['Faktura', 'Číslo faktury', 'DIČ', 'Neznámá kategorie']
        for text in test_texts:
            class_id = excel_mapping.get(text, 0)
            class_name = utils.get_key_class_name(class_id)
            print(f"      '{text}' → ID {class_id} → '{class_name}'")
    
    print("\n6. Ukázka použití v kódu:")
    print("""
   # Zobrazení value_class v preview
   value_name = utils.get_value_class_name(row['value_class'])
   
   # Klasifikace klíčů s Classifier
   mapping = utils.load_key_class_mapping_from_xlsx()
   classifier.set_category_id_mapping(mapping)
   category_id, similarity = classifier.classify(text, use_numeric_ids=True)
   category_name = utils.get_key_class_name(category_id)
   
   # Zobrazení result_class v preview
   result_name = utils.get_result_class_name(row['result_class'])
   """)
    
    print("\n✅ Kompletní test číselníků dokončen!")
    print("\n💡 Všechny tři číselníky jsou nyní centralizované a konzistentní:")
    print("   - VALUE_CLASS_REGISTRY: Automatická klasifikace typů hodnot")
    print("   - KEY_CLASS_REGISTRY: Klasifikace klíčů z Excel listů (pro Classifier)")
    print("   - RESULT_CLASS_REGISTRY: Manuální klasifikace kategorií (pro preview)")

if __name__ == "__main__":
    test_complete_registries()
