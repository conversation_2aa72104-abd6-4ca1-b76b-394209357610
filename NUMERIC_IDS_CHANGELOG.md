# Změny v třídě Classifier - Podpora číselných identifikátorů

## Přehled změn

Třída `Classifier` byla rozšířena o podporu číselných identifikátorů kategorií místo pouze textových názvů. Tato změna umožňuje ukládání výsledků klasifikace jako celá čísla, což je vhodnější pro další zpracování a analýzu dat.

## Nové funkce

### 1. Mapování kategorií na číselné identifikátory

- **Výchozí mapování**: Třída nyní obsahuje výchozí mapování mezi názvy kategorií a číselnými identifikátory
- **Vlastní mapování**: Možnost nastavit vlastní mapování pomocí metody `set_category_id_mapping()`
- **Automatické ukládání/načítání**: Mapování se <PERSON>ky ukládá a načítá spolu s centroidy

### 2. Nové metody

#### `set_category_id_mapping(mapping)`
Nastaví vlastní mapování názvů kategorií na číselné identifikátory.

**Parametry:**
- `mapping` (dict): Slovník mapující názvy kategorií na číselné identifikátory

**Příklad:**
```python
classifier.set_category_id_mapping({
    "Faktura": 1,
    "Datum": 2,
    "Částka": 3
})
```

#### `get_category_id(category_name)`
Vrátí číselný identifikátor pro daný název kategorie.

**Parametry:**
- `category_name` (str): Název kategorie

**Návratová hodnota:**
- `int`: Číselný identifikátor kategorie nebo 0 pro neznámou kategorii

### 3. Rozšířené metody

#### `classify(phrase, use_numeric_ids=False)`
Metoda `classify` byla rozšířena o parametr `use_numeric_ids`.

**Nové parametry:**
- `use_numeric_ids` (bool): Pokud True, vrátí číselný identifikátor místo názvu kategorie

**Návratová hodnota:**
- Pokud `use_numeric_ids=False`: `(název_kategorie, podobnost)`
- Pokud `use_numeric_ids=True`: `(číselný_identifikátor, podobnost)`

#### `batch_classify(df, ..., use_numeric_ids=False)`
Metoda `batch_classify` byla rozšířena o parametr `use_numeric_ids`.

**Nové parametry:**
- `use_numeric_ids` (bool): Pokud True, ukládá číselné identifikátory místo názvů kategorií

**Chování:**
- Pokud `use_numeric_ids=False`: Ukládá názvy kategorií (str) nebo None
- Pokud `use_numeric_ids=True`: Ukládá číselné identifikátory (int) nebo 0

## Výchozí mapování kategorií

```python
{
    "Číslo faktury": 1,
    "Datum vystaven": 2,
    "Datum splatnosti": 3,
    "DUZP": 4,
    "Číslo objednávky": 5,
    "Variabilní symbol": 6,
    "DIČ plátce": 7,
    "IČO plátce": 8,
    "DIČ dodavatele": 9,
    "IČO dodavatele": 10,
    "IBAN": 11,
    "Číslo účtu": 12,
    "Kód banky": 13,
    "Sazba DPH": 14,
    "Základ DPH": 15,
    "Částka celkem s DPH": 16,
    "Celková částka k úhradě": 17,
    "Měna": 18
}
```

## Příklady použití

### Základní použití s číselnými identifikátory

```python
from Classifier import Classifier

# Inicializace
classifier = Classifier(model_name='paraphrase-multilingual-mpnet-base-v2')
classifier.load_model('model_paraphrase-multilingual-mpnet-base-v2')

# Jednotlivá klasifikace
category_id, similarity = classifier.classify("číslo faktury", use_numeric_ids=True)
print(f"Kategorie: {category_id}, Podobnost: {similarity}")  # Kategorie: 1, Podobnost: 0.85

# Batch klasifikace
df = classifier.batch_classify(
    df=df,
    text_column='text',
    class_column='category_id',
    use_numeric_ids=True
)
```

### Vlastní mapování kategorií

```python
# Nastavení vlastního mapování
custom_mapping = {
    "Faktura": 1,
    "Datum": 2,
    "Částka": 3,
    "Dodavatel": 4
}
classifier.set_category_id_mapping(custom_mapping)

# Použití s vlastním mapováním
category_id, similarity = classifier.classify("faktura", use_numeric_ids=True)
```

## Zpětná kompatibilita

Všechny změny jsou zpětně kompatibilní:
- Výchozí chování metod `classify` a `batch_classify` zůstává nezměněno
- Existující kód bude fungovat bez úprav
- Nový parametr `use_numeric_ids` má výchozí hodnotu `False`

## Změny v main.py

Soubor `main.py` byl upraven pro použití číselných identifikátorů:

```python
# Klasifikace textů s číselnými identifikátory
df = classifier.batch_classify(
    df=df,
    text_column='text',
    class_column='key_class',
    similarity_column='similarity',
    threshold=0.85,
    use_numeric_ids=True  # Nový parametr
)
```

## Ukládání a načítání

### Nový formát souborů
Centroidy se nyní ukládají spolu s mapováním kategorií v novém formátu:

```python
{
    'centroids': {...},  # Původní centroidy
    'category_id_mapping': {...}  # Mapování kategorií
}
```

### Kompatibilita se starými soubory
Třída automaticky rozpozná starý formát souborů a použije výchozí mapování.

## Testování

Byly vytvořeny testy pro ověření funkčnosti:
- `test_numeric_classification.py` - Komplexní test všech nových funkcí
- `test_main_numeric.py` - Test integrace s hlavní funkcionalitou

## Výhody nových změn

1. **Efektivnější ukládání**: Číselné identifikátory zabírají méně místa než textové řetězce
2. **Rychlejší zpracování**: Porovnávání čísel je rychlejší než porovnávání řetězců
3. **Lepší integrace**: Číselné identifikátory se lépe hodí pro databáze a další systémy
4. **Konzistentní rozhraní**: Jednotné rozhraní napříč celou aplikací
5. **Zpětná kompatibilita**: Existující kód funguje bez změn
