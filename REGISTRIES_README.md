# Centr<PERSON><PERSON><PERSON> číselníky pro klasifikaci

Tento dokument popisuje systém centráln<PERSON>ch číselníků pro klasifikaci textových prvků v aplikaci.

## Přehled

Aplikace používá tři typy klasifikace:

1. **value_class** - <PERSON><PERSON><PERSON> klasifikace typů hodnot (datum, číslo, IBAN, atd.)
2. **key_class** - Klasifikace klíčů na základě názvů listů z training_set.xlsx (Classifier)
3. **result_class** - Manuální klasifikace kategorií faktury (č<PERSON>lo faktury, datum vystavení, atd.)

## Číselníky v utils.py

### VALUE_CLASS_REGISTRY
Číselník pro automatickou klasifikaci typů hodnot:

```python
VALUE_CLASS_REGISTRY = {
    0: "Neznámý typ",
    1: "<PERSON><PERSON>", 
    2: "Procenta",
    3: "Des<PERSON><PERSON>é <PERSON>",
    4: "Číslo účtu",
    5: "Alfanumerický kód",
    6: "IBAN",
    7: "DIČ",
    8: "Číslo"
}
```

### KEY_CLASS_REGISTRY
Číselník pro klasifikaci klíčů na základě názvů listů z training_set.xlsx:

```python
KEY_CLASS_REGISTRY = {
    0: "Ostatní",
    1: "Faktura",
    2: "Dobropis",
    3: "Zálohová faktura",
    4: "Záloha",
    5: "Číslo faktury",
    6: "Variabilní symbol",
    7: "Objednávka",
    8: "Číslo objednávky",
    9: "Dodavatel",
    10: "Odběratel",
    11: "Číslo účtu",
    12: "Datum vystavení",
    13: "Datum splatnosti",
    14: "DUZP",
    15: "Celkem k úhradě",
    16: "Základ DPH",
    17: "Sazba DPH",
    18: "IČO",
    19: "Číslo dodacího listu",
    20: "DIČ",
    21: "IBAN",
    22: "DPH"
}
```

### RESULT_CLASS_REGISTRY
Číselník pro manuální klasifikaci kategorií faktury:

```python
RESULT_CLASS_REGISTRY = {
    0: "Ostatní",
    1: "Číslo faktury",
    2: "Datum vystavení",
    3: "Datum splatnosti",
    4: "DUZP",
    5: "Číslo objednávky",
    6: "Variabilní symbol",
    7: "DIČ plátce",
    8: "IČO plátce",
    9: "DIČ dodavatele",
    10: "IČO dodavatele",
    11: "IBAN",
    12: "Číslo účtu",
    13: "Kód banky",
    14: "Sazba DPH",
    15: "Základ DPH",
    16: "Částka celkem s DPH",
    17: "Celková částka k úhradě",
    18: "Měna"
}
```

## API funkce

### get_value_class_name(value_class_id)
Vrátí název pro value_class ID.

```python
name = utils.get_value_class_name(1)  # "Datum"
name = utils.get_value_class_name(99)  # "Neznámý typ 99"
```

### get_key_class_name(key_class_id)
Vrátí název pro key_class ID.

```python
name = utils.get_key_class_name(1)  # "Faktura"
name = utils.get_key_class_name(99)  # "Neznámý klíč 99"
```

### get_result_class_name(result_class_id)
Vrátí název pro result_class ID.

```python
name = utils.get_result_class_name(1)  # "Číslo faktury"
name = utils.get_result_class_name(99)  # "Neznámá kategorie 99"
```

### get_available_key_classes()
Vrátí seznam dostupných klíčů jako (id, název).

```python
classes = utils.get_available_key_classes()
# [(0, 'Ostatní'), (1, 'Faktura'), ...]
```

### get_available_result_classes()
Vrátí seznam dostupných kategorií pro dropdown jako (id, název).

```python
classes = utils.get_available_result_classes()
# [(0, 'Ostatní'), (1, 'Číslo faktury'), ...]
```

### get_value_class_registry()
Vrátí kopii VALUE_CLASS_REGISTRY.

### get_key_class_registry()
Vrátí kopii KEY_CLASS_REGISTRY.

### get_result_class_registry()
Vrátí kopii RESULT_CLASS_REGISTRY.

## Funkce pro práci s Excel souborem

### load_key_class_mapping_from_xlsx(xlsx_path)
Načte názvy listů z Excel souboru a vytvoří mapování pro Classifier.

```python
mapping = utils.load_key_class_mapping_from_xlsx('training_data/training_set.xlsx')
# {'Faktura': 1, 'Dobropis': 2, 'Číslo faktury': 5, ...}
```

### update_key_class_registry_from_xlsx(xlsx_path)
Aktualizuje KEY_CLASS_REGISTRY na základě názvů listů z Excel souboru.

```python
success = utils.update_key_class_registry_from_xlsx('training_data/training_set.xlsx')
if success:
    print("Číselník byl aktualizován")
```

## Integrace s Classifier třídou

Classifier třída **nemá výchozí mapování** v konstruktoru. Mapování se načítá třemi způsoby:

### Způsob 1: Automatické načtení během fine-tuningu (doporučeno)
```python
from Classifier import Classifier

classifier = Classifier(model_name='your-model')
classifier.fine_tune(data_dir='training_data')  # Mapování se načte automaticky z Excel
```

### Způsob 2: Explicitní nastavení mapování
```python
from Classifier import Classifier
import utils

# Načtení mapování z Excel souboru
mapping = utils.load_key_class_mapping_from_xlsx()

# Vytvoření a konfigurace Classifier
classifier = Classifier(model_name='your-model')
classifier.set_category_id_mapping(mapping)

# Klasifikace s číselnými ID
category_id, similarity = classifier.classify("faktura", use_numeric_ids=True)
category_name = utils.get_key_class_name(category_id)
```

### Způsob 3: Načtení uloženého modelu s mapováním
```python
from Classifier import Classifier

classifier = Classifier(model_name='your-model')
classifier.load_model('path/to/saved/model')  # Mapování se načte ze souboru
```

## Použití v preview

V `preview_qt.py` se číselníky používají pro:

1. **Zobrazení key_class** - červené popisky nad bounding boxy (pro key_class > 0)
2. **Zobrazení value_class** - modré popisky pod bounding boxy (pro value_class > 0)
3. **Dropdown seznamy** - pro výběr result_class kategorií (pro prvky s value_class > 0)
4. **Export statistik** - pro zobrazení názvů místo čísel

### Barevné kódování v preview:
- **🔴 Červená** (nad bounding boxem): key_class názvy z KEY_CLASS_REGISTRY (pro key_class > 0)
- **🔵 Modrá** (pod bounding boxem): value_class názvy z VALUE_CLASS_REGISTRY (pro value_class > 0)
- **📋 Dropdown** (vlevo od bounding boxu): result_class výběr z RESULT_CLASS_REGISTRY (pro value_class > 0)

### Opravy v preview_qt.py:
- ✅ **Opraveno načítání key_class**: `df.loc[i, 'class']` → `df.loc[i, 'key_class']`
- ✅ **Opravena podmínka**: `if key_class is not None` → `if key_class > 0`
- ✅ **Zobrazení názvů**: `str(key_class)` → `utils.get_key_class_name(key_class)`
- ✅ **Vylepšený styl**: Červené popisky s bílým pozadím pro lepší čitelnost
- ✅ **Ochrana pozice**: Popisky se zobrazují minimálně na y=10 (nejsou překryté)

## Výhody centrálních číselníků

1. **Konzistence** - Všechny části aplikace používají stejné názvy
2. **Snadná údržba** - Změna názvu na jednom místě
3. **Rozšiřitelnost** - Snadné přidání nových kategorií
4. **Bezpečnost** - Kontrola neexistujících ID s fallback názvy

## Testování

Spusťte test číselníků:

```bash
python3 test_registries.py
```

Test ověří:
- Načtení číselníků
- Funkce pro získání názvů
- Správné chování pro neexistující ID
- Dostupné kategorie pro dropdown
