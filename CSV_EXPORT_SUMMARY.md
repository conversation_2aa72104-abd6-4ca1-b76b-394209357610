# CSV Export - Souhrn implementace

## Přehled změn

Implementoval jsem novou funkcionalitu pro export do CSV podle vašich požadavků:

### 1. Vytvoření složky Results ✅
- Automatick<PERSON> vytvoř<PERSON> slo<PERSON>ky `Results` pokud neexistuje
- Všechny CSV soubory se ukládají do této složky

### 2. Pojmenování souborů podle PDF ✅
- CSV soubory se pojmenovávají podle názvu vstupního PDF
- Příklad: `data/F3.pdf` → `Results/F3.csv`
- Existující soubory se přepisují

### 3. Filtrování dat ✅
- Exportují se pouze řádky kde `key_class > 0` AND `result_class > 0`
- Ostatní řádky se ignorují

### 4. Řádek "page" ✅
- Na konec se přidává řádek s textem "page"
- Bounding box: `left=0, top=0, width=page_width, height=page_height`
- Všechny třídy nastaveny na 0: `key_class=0, value_class=0, result_class=0`

## Nové funkce v utils.py

### `get_page_dimensions(file_path)`
- Získává rozměry stránky z PDF souboru
- Vrací tuple `(page_width, page_height)` v pixelech
- Fallback na výchozí rozměry 800x1200 při chybě

### `ensure_results_directory()`
- Zajišťuje existenci složky "Results"
- Vytvoří složku pokud neexistuje

### `get_output_filename(file_path)`
- Generuje název výstupního CSV souboru
- Formát: `Results/{basename}.csv`

### `export_filtered_results(df, file_path)`
- Hlavní export funkce
- Filtruje data podle podmínek
- Přidává řádek "page"
- Ukládá do CSV

## Upravené soubory

### 1. `utils.py`
- Přidány nové import: `os`, `pdf2image`, `numpy`
- Implementovány 4 nové funkce pro export

### 2. `preview_qt.py`
- `load_document()`: Ukládá `file_path` jako atribut
- `export_results()`: Používá novou export funkci místo základního CSV

### 3. `main.py`
- Používá `utils.export_filtered_results()` místo `to_csv()`
- Fallback na původní metodu při chybě

### 4. `demo_results_export.py`
- Aktualizované instrukce pro uživatele
- Používá novou export funkci

## Testovací soubory

### `test_csv_export.py`
- Testuje všechny nové funkce
- Ověřuje filtrování dat
- Kontroluje strukturu výstupních souborů

### `test_interactive_export.py`
- Testuje interaktivní export z GUI
- Simuluje reálné použití

## Příklad použití

```python
import utils
import pandas as pd

# Vytvoření testovacích dat
df = pd.DataFrame({
    'text': ['FAKTURA', 'poznámka', 'F2024001'],
    'left': [50, 200, 350],
    'top': [50, 50, 50],
    'width': [120, 100, 120],
    'height': [25, 25, 25],
    'key_class': [1, 0, 5],      # Pouze 1. a 3. řádek má key_class > 0
    'value_class': [0, 0, 5],
    'result_class': [1, 0, 1]    # Pouze 1. a 3. řádek má result_class > 0
})

# Export - exportují se pouze řádky 1 a 3 + řádek "page"
output_file = utils.export_filtered_results(df, 'data/test.pdf')
# Výsledek: Results/test.csv s 3 řádky
```

## Výstupní formát CSV

```csv
text,left,top,width,height,key_class,value_class,result_class
FAKTURA,50,50,120,25,1,0,1
F2024001,350,50,120,25,5,5,1
page,0,0,2481,3508,0,0,0
```

## Ověření funkcionality

Spusťte testy:
```bash
python3 test_csv_export.py
python3 test_interactive_export.py
```

Nebo použijte interaktivní demo:
```bash
python3 demo_results_export.py
python3 main.py
```

## Klíčové vlastnosti

1. **Automatické filtrování**: Pouze relevantní data (key_class > 0 AND result_class > 0)
2. **Konzistentní pojmenování**: CSV soubory odpovídají názvům PDF
3. **Organizace souborů**: Všechny výstupy v složce Results
4. **Metadata stránky**: Řádek "page" s rozměry dokumentu
5. **Robustnost**: Fallback mechanismy při chybách
6. **Kompatibilita**: Zachována funkčnost existujícího kódu

Všechny požadavky byly úspěšně implementovány a otestovány! 🎉
