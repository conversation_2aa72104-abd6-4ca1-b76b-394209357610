import preview_qt as preview
import utils
from Classifier import Classifier
from OCR import ocr

file_path = 'data/Faktura.pdf'

print("🔍 OCR Document Processing with PyQt5 Viewer")
print("=" * 45)

# Step 1: OCR Processing
print("1. Performing OCR...")
df = ocr.do(file_path)
print(f"   ✓ Found {len(df)} raw text elements")


# Step 2: Text Processing
print("2. Processing text data...")
df = utils.merge_texts(df)
print(f"   ✓ After merging: {len(df)} elements")



utils.classify_batch_values(df)
print("   ✓ Value classification completed")


df = utils.clean_texts(df)
print("   ✓ Text cleaning completed")


# Step 3: Key Classification
print("3. Performing key classification...")

# Inicializace klasifikátoru
classifier = Classifier(model_name='paraphrase-multilingual-mpnet-base-v2')

# Načtení mapování kategorií z Excel souboru
print("   Loading category mapping from Excel...")
mapping = utils.load_key_class_mapping_from_xlsx()
if mapping:
    print(f"   ✓ Loaded {len(mapping)} categories from Excel")
    classifier.set_category_id_mapping(mapping)
else:
    print("   ⚠️  Failed to load category mapping from Excel")

# Načtení modelu
print("   Loading trained model...")
model_loaded = classifier.load_model('model_paraphrase-multilingual-mpnet-base-v2')
if not model_loaded:
    print("   ⚠️  Failed to load model, key classification will be skipped")
    # Přidáme prázdný key_class sloupec
    df['key_class'] = 0
    df['similarity'] = 0.0
else:
    print("   ✓ Model loaded successfully")

    # Klasifikace textů s číselnými identifikátory
    print("   Classifying texts...")
    df = classifier.batch_classify(
        df=df,
        text_column='text',
        class_column='key_class',
        similarity_column='similarity',
        threshold=0.85,
        use_numeric_ids=True  # Používáme číselné identifikátory místo názvů kategorií
    )

    # Statistiky key_class
    key_classified = len(df[df['key_class'] > 0])
    print(f"   ✓ Key classification completed: {key_classified}/{len(df)} elements classified")


# Step 4: Interactive Viewer
print("\n4. Opening PyQt5 interactive viewer...")
print("   🎯 Features:")
print("   - Mouse wheel zoom (smooth & intuitive)")
print("   - Middle mouse or Ctrl+drag pan")
print("   - Zoom In/Out/Reset buttons")
print("   - Toggle bounding boxes visibility")
print("   - High-quality document rendering")
print("   - Color-coded classification labels")
print("   - Interactive dropdowns for elements with value_class > 0")

# Zobrazíme statistiky před interakcí
interactive_elements = len(df[df['value_class'] > 0])
print(f"   📊 {interactive_elements} elements will have interactive dropdowns")

# Spustíme interaktivní viewer a získáme výsledky
print("\n   💡 Use dropdown menus to classify elements, then close the window")
updated_df = preview.show_document_qt(file_path, df, return_results=True)

# Step 5: Results Analysis
print("\n5. Analyzing results...")
if updated_df is not None:
    # Zkontrolujeme, zda byl přidán result_class sloupec
    if 'result_class' in updated_df.columns:
        total_elements = len(updated_df)
        classified_elements = len(updated_df[updated_df['result_class'] > 0])

        print(f"   ✓ Total elements: {total_elements}")
        print(f"   ✓ Result classified elements: {classified_elements}")
        print(f"   ✓ Result unclassified elements: {total_elements - classified_elements}")

        # Statistiky key_class
        if 'key_class' in updated_df.columns:
            key_classified = len(updated_df[updated_df['key_class'] > 0])
            print(f"   ✓ Key classified elements: {key_classified}")
            print(f"   ✓ Key unclassified elements: {total_elements - key_classified}")

        # Zobrazíme rozložení podle result_class
        if classified_elements > 0:
            print(f"\n   📋 Result class breakdown:")
            result_counts = updated_df[updated_df['result_class'] > 0]['result_class'].value_counts().sort_index()

            for class_id, count in result_counts.items():
                class_text = utils.get_result_class_name(class_id)
                print(f"      {class_id} ({class_text}): {count} elements")

        # Zobrazíme rozložení podle key_class
        if 'key_class' in updated_df.columns:
            key_classified = len(updated_df[updated_df['key_class'] > 0])
            if key_classified > 0:
                print(f"\n   🔑 Key class breakdown:")
                key_counts = updated_df[updated_df['key_class'] > 0]['key_class'].value_counts().sort_index()

                for class_id, count in key_counts.items():
                    class_text = utils.get_key_class_name(class_id)
                    print(f"      {class_id} ({class_text}): {count} elements")

        # Uložíme výsledky pomocí nové export funkce
        try:
            output_file = utils.export_filtered_results(updated_df, file_path)
            print(f"\n   💾 Filtered results exported to: {output_file}")
        except Exception as e:
            print(f"\n   ❌ Export error: {e}")
            # Fallback - uložíme všechna data
            output_file = 'classified_results.csv'
            updated_df.to_csv(output_file, index=False)
            print(f"   💾 All results saved to: {output_file}")

        # Zobrazíme ukázku klasifikovaných prvků
        classified_sample = updated_df[updated_df['result_class'] > 0][['text', 'value_class', 'key_class', 'result_class']].head()
        if not classified_sample.empty:
            print(f"\n   🔍 Sample of classified elements:")
            for _, row in classified_sample.iterrows():
                value_class_name = utils.get_value_class_name(row['value_class'])
                key_class_name = utils.get_key_class_name(row['key_class'])
                result_class_name = utils.get_result_class_name(row['result_class'])
                print(f"      '{row['text']}' → value:{value_class_name}, key:{key_class_name}, result:{result_class_name}")
    else:
        print("   ⚠️  No result_class column found in returned DataFrame")
else:
    print("   ⚠️  No results returned from interactive viewer")

print("\n✅ Processing completed!")
