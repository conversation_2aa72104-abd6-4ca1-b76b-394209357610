import preview_qt as preview
import utils
from Classifier import Classifier
from OCR import ocr

file_path = 'data/Faktura.pdf'

print("🔍 OCR Document Processing with PyQt5 Viewer")
print("=" * 45)

# Step 1: OCR Processing
print("1. Performing OCR...")
df = ocr.do(file_path)
print(f"   ✓ Found {len(df)} raw text elements")


# Step 2: Text Processing
print("2. Processing text data...")
df = utils.merge_texts(df)
print(f"   ✓ After merging: {len(df)} elements")



utils.classify_batch_values(df)
print("   ✓ Value classification completed")


df = utils.clean_texts(df)
print("   ✓ Text cleaning completed")


# Step 3: Key Classification
print("3. Performing key classification...")

# Inicializace klasifikátoru
classifier = Classifier(model_name='paraphrase-multilingual-mpnet-base-v2')

# Načtení mapování kategorií z Excel souboru
print("   Loading category mapping from Excel...")
mapping = utils.load_key_class_mapping_from_xlsx()
if mapping:
    print(f"   ✓ Loaded {len(mapping)} categories from Excel")
    classifier.set_category_id_mapping(mapping)
else:
    print("   ⚠️  Failed to load category mapping from Excel")

# Načtení modelu
print("   Loading trained model...")
model_loaded = classifier.load_model('model_paraphrase-multilingual-mpnet-base-v2')
if not model_loaded:
    print("   ⚠️  Failed to load model, key classification will be skipped")
    # Přidáme prázdný key_class sloupec
    df['key_class'] = 0
    df['similarity'] = 0.0
else:
    print("   ✓ Model loaded successfully")

    # Klasifikace textů s číselnými identifikátory
    print("   Classifying texts...")
    df = classifier.batch_classify(
        df=df,
        text_column='text',
        class_column='key_class',
        similarity_column='similarity',
        threshold=0.85,
        use_numeric_ids=True  # Používáme číselné identifikátory místo názvů kategorií
    )

    # Statistiky key_class
    key_classified = len(df[df['key_class'] > 0])
    print(f"   ✓ Key classification completed: {key_classified}/{len(df)} elements classified")


# Step 3: Interactive Viewer
print("\n3. Opening PyQt5 interactive viewer...")
print("   🎯 Features:")
print("   - Mouse wheel zoom (smooth & intuitive)")
print("   - Middle mouse or Ctrl+drag pan")
print("   - Zoom In/Out/Reset buttons")
print("   - Toggle bounding boxes visibility")
print("   - High-quality document rendering")
print("   - Color-coded classification labels")
print("   - Interactive dropdowns for elements with value_class > 0")

# Zobrazíme statistiky před interakcí
interactive_elements = len(df[df['value_class'] > 0])
print(f"   📊 {interactive_elements} elements will have interactive dropdowns")

# Spustíme interaktivní viewer a získáme výsledky
print("\n   💡 Use dropdown menus to classify elements, then close the window")
updated_df = preview.show_document_qt(file_path, df, return_results=True)

# Step 4: Results Analysis
print("\n4. Analyzing results...")
if updated_df is not None:
    # Zkontrolujeme, zda byl přidán result_class sloupec
    if 'result_class' in updated_df.columns:
        total_elements = len(updated_df)
        classified_elements = len(updated_df[updated_df['result_class'] > 0])

        print(f"   ✓ Total elements: {total_elements}")
        print(f"   ✓ Classified elements: {classified_elements}")
        print(f"   ✓ Unclassified elements: {total_elements - classified_elements}")

        # Zobrazíme rozložení podle result_class
        if classified_elements > 0:
            print(f"\n   📋 Classification breakdown:")
            result_counts = updated_df[updated_df['result_class'] > 0]['result_class'].value_counts().sort_index()

            # Mapování číselných hodnot na textové popisy
            class_mapping = {
                1: "Číslo faktury",
                2: "Datum vystaven",
                3: "Datum splatnosti",
                4: "DUZP",
                5: "Číslo objednávky",
                6: "Variabilní symbol",
                7: "DIČ plátce",
                8: "IČO plátce",
                9: "DIČ dodavatele",
                10: "IČO dodavatele",
                11: "IBAN",
                12: "Číslo účtu",
                13: "Kód banky",
                14: "Sazba DPH",
                15: "Základ DPH",
                16: "Částka celkem s DPH",
                17: "Celková částka k úhradě",
                18: "Měna",
                0: "Ostatní"
            }

            for class_id, count in result_counts.items():
                class_text = class_mapping.get(class_id, f"Neznámá třída {class_id}")
                print(f"      {class_id} ({class_text}): {count} elements")

        # Uložíme výsledky
        output_file = 'classified_results.csv'
        updated_df.to_csv(output_file, index=False)
        print(f"\n   💾 Results saved to: {output_file}")

        # Zobrazíme ukázku klasifikovaných prvků
        classified_sample = updated_df[updated_df['result_class'] > 0][['text', 'value_class', 'result_class']].head()
        if not classified_sample.empty:
            print(f"\n   🔍 Sample of classified elements:")
            for _, row in classified_sample.iterrows():
                class_text = class_mapping.get(row['result_class'], f"Neznámá třída {row['result_class']}")
                print(f"      '{row['text']}' (type {row['value_class']}) → {row['result_class']} ({class_text})")
    else:
        print("   ⚠️  No result_class column found in returned DataFrame")
else:
    print("   ⚠️  No results returned from interactive viewer")

print("\n✅ Processing completed!")
