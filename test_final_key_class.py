#!/usr/bin/env python3
"""
Finální test key_class zobrazování v preview.
"""

import pandas as pd
import utils

def create_realistic_test_data():
    """Vyt<PERSON>ř<PERSON> realistic<PERSON> testovací data s key_class hodnotami."""
    
    # Realistická data faktury s key_class hodnotami
    test_data = {
        'text': [
            'FAKTURA',                    # key_class: 1 (Faktura)
            'F-2024-001234',              # key_class: 5 (<PERSON><PERSON><PERSON>)
            '15.01.2024',                 # key_class: 12 (<PERSON><PERSON>)
            '29.01.2024',                 # key_class: 13 (<PERSON><PERSON>)
            'ABC Company s.r.o.',         # key_class: 9 (Dodavatel)
            'XYZ Client a.s.',            # key_class: 10 (Odběratel)
            'CZ12345678',                 # key_class: 20 (DIČ)
            'VS: 2024001234',             # key_class: 6 (<PERSON><PERSON><PERSON><PERSON><PERSON> symbol)
            '************************',   # key_class: 21 (IBAN)
            '15 750,00 Kč',               # key_class: 15 (<PERSON><PERSON><PERSON>)
            '13 000,00 Kč',               # key_class: 16 (<PERSON><PERSON><PERSON>)
            '21%',                        # key_class: 17 (Sazba DPH)
            'Popis služby',               # key_class: 0 (Ostatní)
            'Poznámka',                   # key_class: 0 (Ostatní)
            'Podpis'                      # key_class: 0 (Ostatní)
        ],
        'left': [50, 300, 50, 300, 50, 300, 50, 300, 50, 300, 50, 300, 50, 300, 50],
        'top': [50, 50, 100, 100, 150, 150, 200, 200, 250, 250, 300, 300, 350, 400, 450],
        'width': [120, 150, 100, 100, 180, 160, 120, 160, 250, 120, 120, 80, 150, 100, 80],
        'height': [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25],
        'key_class': [1, 5, 12, 13, 9, 10, 20, 6, 21, 15, 16, 17, 0, 0, 0],
        'value_class': [0, 5, 1, 1, 0, 0, 7, 8, 6, 3, 3, 2, 0, 0, 0],
        'result_class': [0, 1, 2, 3, 0, 0, 7, 6, 11, 17, 15, 14, 0, 0, 0]
    }
    
    return pd.DataFrame(test_data)

def test_final_key_class():
    """Finální test key_class zobrazování."""
    
    print("✅ Finální test key_class zobrazování")
    print("=" * 50)
    
    # Vytvoříme realistická testovací data
    df = create_realistic_test_data()
    
    print("1. Realistická testovací data faktury:")
    print("   Text                         | key_class | Očekávaný červený popisek")
    print("   " + "-" * 75)
    
    for i, row in df.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        display = "✓" if row['key_class'] > 0 else "✗"
        print(f"   {row['text']:<27} | {row['key_class']:>9} | {key_name} ({display})")
    
    # Uložíme data pro testování
    output_file = 'final_key_class_test.csv'
    df.to_csv(output_file, index=False)
    
    print(f"\n2. Data uložena do: {output_file}")
    
    # Statistiky
    key_elements = df[df['key_class'] > 0]
    value_elements = df[df['value_class'] > 0]
    
    print(f"\n3. Statistiky:")
    print(f"   Celkem prvků: {len(df)}")
    print(f"   Prvky s key_class > 0: {len(key_elements)} (červené popisky)")
    print(f"   Prvky s value_class > 0: {len(value_elements)} (modré popisky + dropdown)")
    
    print(f"\n4. Očekávané červené popisky nad bounding boxy:")
    for i, row in key_elements.iterrows():
        key_name = utils.get_key_class_name(row['key_class'])
        print(f"      '{row['text']}' → '{key_name}'")
    
    print(f"\n5. Očekávané modré popisky pod bounding boxy:")
    for i, row in value_elements.iterrows():
        value_name = utils.get_value_class_name(row['value_class'])
        print(f"      '{row['text']}' → '{value_name}'")
    
    print(f"\n6. Pro testování spusťte:")
    print(f"   python3 -c \"")
    print(f"import pandas as pd")
    print(f"from preview_qt import show_document_qt")
    print(f"df = pd.read_csv('{output_file}')")
    print(f"# Potřebujete PDF soubor pro show_document_qt")
    print(f"# Nebo použijte DocumentViewer přímo s dummy obrázkem\"")
    
    print(f"\n7. Alternativní test s DocumentViewer:")
    print(f"   python3 -c \"")
    print(f"import pandas as pd, sys, numpy as np")
    print(f"from PyQt5.QtWidgets import QApplication")
    print(f"from preview_qt import DocumentViewer")
    print(f"app = QApplication(sys.argv)")
    print(f"df = pd.read_csv('{output_file}')")
    print(f"viewer = DocumentViewer()")
    print(f"viewer.load_image_from_array(np.ones((800, 1000, 3), dtype=np.uint8) * 255)")
    print(f"viewer.add_bounding_boxes(df)")
    print(f"viewer.show()")
    print(f"viewer.resize(1200, 900)")
    print(f"sys.exit(app.exec_())\"")
    
    print(f"\n✅ Finální test připraven!")
    print(f"\n💡 Opravy v preview_qt.py:")
    print(f"   ✅ key_class se načítá ze správného sloupce 'key_class'")
    print(f"   ✅ Podmínka 'if key_class > 0' funguje správně")
    print(f"   ✅ Zobrazují se názvy z číselníku místo čísel")
    print(f"   ✅ Červené popisky s bílým pozadím pro lepší čitelnost")
    print(f"   ✅ Pozice nad bounding boxem s ochranou proti překrytí")

if __name__ == "__main__":
    test_final_key_class()
