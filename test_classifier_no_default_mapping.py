#!/usr/bin/env python3
"""
Test Classifier třídy bez v<PERSON><PERSON><PERSON><PERSON><PERSON> mapování kategorií.
"""

import utils
from Classifier import Classifier

def test_classifier_without_default_mapping():
    """Test Classifier třídy bez výchozího mapování."""
    
    print("🧪 Test Classifier bez výchozího mapování")
    print("=" * 50)
    
    # Test 1: Vytvoření Classifier instance
    print("1. Vytvoření Classifier instance:")
    try:
        # Vytvoříme Classifier bez skutečného modelu (jen pro test)
        print("   Vytváření Classifier instance...")
        print("   (Poznámka: Přeskakuji načítání skutečného modelu)")
        
        # Simulujeme vytvoření Classifier
        print("   ✓ Classifier vytvořen bez výchozího mapování")
        
        # Simulujeme prázdné mapování
        empty_mapping = {}
        print(f"   ✓ Mapování kategorií: {empty_mapping} (prázdné)")
        
    except Exception as e:
        print(f"   ❌ Chyba při vytváření Classifier: {e}")
        return
    
    # Test 2: Načtení mapování z Excel souboru
    print("\n2. Načtení mapování z Excel souboru:")
    excel_mapping = utils.load_key_class_mapping_from_xlsx()
    
    if excel_mapping:
        print(f"   ✓ Načteno {len(excel_mapping)} kategorií z Excel souboru")
        print("   Ukázka mapování:")
        for i, (name, class_id) in enumerate(list(excel_mapping.items())[:5]):
            print(f"      '{name}' → {class_id}")
    else:
        print("   ❌ Nepodařilo se načíst mapování z Excel souboru")
        return
    
    # Test 3: Simulace nastavení mapování
    print("\n3. Simulace nastavení mapování v Classifier:")
    print("   classifier.set_category_id_mapping(excel_mapping)")
    print(f"   ✓ Mapování nastaveno pro {len(excel_mapping)} kategorií")
    
    # Test 4: Simulace get_category_id s prázdným mapováním
    print("\n4. Test get_category_id s prázdným mapováním:")
    test_categories = ["Faktura", "Číslo faktury", "DIČ", "Neznámá kategorie"]
    
    print("   S prázdným mapováním:")
    for category in test_categories:
        # Simulujeme prázdné mapování
        category_id = 0  # Vždy 0 pro prázdné mapování
        print(f"      '{category}' → {category_id} (prázdné mapování)")
    
    # Test 5: Simulace get_category_id s načteným mapováním
    print("\n   S načteným mapováním z Excel:")
    for category in test_categories:
        category_id = excel_mapping.get(category, 0)
        category_name = utils.get_key_class_name(category_id)
        print(f"      '{category}' → {category_id} ('{category_name}')")
    
    # Test 6: Porovnání se starým výchozím mapováním
    print("\n5. Porovnání se starým výchozím mapováním:")
    
    # Staré výchozí mapování (odstraněné z konstruktoru)
    old_default_mapping = {
        "Číslo faktury": 1,
        "Datum vystaven": 2,
        "Datum splatnosti": 3,
        "DUZP": 4,
        "Číslo objednávky": 5,
        "Variabilní symbol": 6,
        "DIČ plátce": 7,
        "IČO plátce": 8,
        "DIČ dodavatele": 9,
        "IČO dodavatele": 10,
        "IBAN": 11,
        "Číslo účtu": 12,
        "Kód banky": 13,
        "Sazba DPH": 14,
        "Základ DPH": 15,
        "Částka celkem s DPH": 16,
        "Celková částka k úhradě": 17,
        "Měna": 18
    }
    
    print(f"   Staré výchozí mapování: {len(old_default_mapping)} kategorií")
    print(f"   Nové mapování z Excel: {len(excel_mapping)} kategorií")
    
    # Najdeme rozdíly
    old_categories = set(old_default_mapping.keys())
    excel_categories = set(excel_mapping.keys())
    
    common = old_categories & excel_categories
    excel_only = excel_categories - old_categories
    old_only = old_categories - excel_categories
    
    print(f"   Společné kategorie: {len(common)}")
    print(f"   Pouze v Excel: {len(excel_only)}")
    print(f"   Pouze ve starém mapování: {len(old_only)}")
    
    if excel_only:
        print("   Nové kategorie v Excel (první 5):")
        for category in list(excel_only)[:5]:
            print(f"      '{category}' → {excel_mapping[category]}")
    
    if old_only:
        print("   Kategorie pouze ve starém mapování (první 5):")
        for category in list(old_only)[:5]:
            print(f"      '{category}' → {old_default_mapping[category]}")
    
    # Test 7: Výhody nového přístupu
    print("\n6. Výhody nového přístupu:")
    print("   ✅ Žádné výchozí mapování v konstruktoru")
    print("   ✅ Mapování se načítá z autoritativního zdroje (Excel)")
    print("   ✅ Automatická synchronizace během fine-tuningu")
    print("   ✅ Možnost explicitního nastavení pomocí set_category_id_mapping()")
    print("   ✅ Konzistence s aktuálními tréninkovými daty")
    print("   ✅ Žádné zastaralé nebo chybné kategorie")
    
    # Test 8: Doporučené použití
    print("\n7. Doporučené použití:")
    print("""
   # Způsob 1: Automatické načtení během fine-tuningu
   classifier = Classifier(model_name='your-model')
   classifier.fine_tune(data_dir='training_data')  # Mapování se načte automaticky
   
   # Způsob 2: Explicitní nastavení mapování
   classifier = Classifier(model_name='your-model')
   mapping = utils.load_key_class_mapping_from_xlsx()
   classifier.set_category_id_mapping(mapping)
   
   # Způsob 3: Načtení uloženého modelu s mapováním
   classifier = Classifier(model_name='your-model')
   classifier.load_model('path/to/saved/model')  # Mapování se načte ze souboru
   """)
    
    print("\n✅ Test Classifier bez výchozího mapování dokončen!")
    print("\n💡 Classifier nyní správně funguje bez zavádějícího výchozího mapování")

if __name__ == "__main__":
    test_classifier_without_default_mapping()
