#!/usr/bin/env python3
"""
Test číselných hodnot v result_class sloupci.
"""

import pandas as pd
import preview_qt as preview
import utils
from OCR import ocr

def test_numeric_values():
    """Test číselných hodnot v result_class."""
    
    print("🔢 Test číselných hodnot v result_class")
    print("=" * 45)
    
    # Načteme dokument
    file_path = 'data/F3.pdf'
    
    print("1. Načítání a zpracování dokumentu...")
    df = ocr.do(file_path)
    df = utils.merge_texts(df)
    utils.classify_batch_values(df)
    df = utils.clean_texts(df)
    
    # Zobrazíme dostupné kategorie
    print("2. Dostupné kategorie pro klasifikaci:")
    categories = [
        (1, "Číslo faktury"),
        (2, "Datum"), 
        (3, "<PERSON>lková částka"),
        (4, "DIČ"),
        (5, "Dodavatel"),
        (6, "Ostatní")
    ]
    
    for cat_id, cat_text in categories:
        print(f"   {cat_id}: {cat_text}")
    
    # Zobrazíme prvky s interaktivními seznamy
    interactive_elements = df[df['value_class'] > 0]
    print(f"\n3. Prvky s interaktivními seznamy ({len(interactive_elements)}):")
    
    for i, (idx, row) in enumerate(interactive_elements.head(5).iterrows()):
        value_class_name = utils.get_value_class_name(row['value_class'])

        print(f"   {i+1}. '{row['text']}' (value_class: {row['value_class']} - {value_class_name})")
    
    if len(interactive_elements) > 5:
        print(f"   ... a dalších {len(interactive_elements) - 5} prvků")
    
    print(f"\n💡 Instrukce:")
    print(f"1. V dropdown seznamech vyberte kategorie pro několik prvků")
    print(f"2. Zobrazí se text, ale uloží se číslo")
    print(f"3. Pokud nevyberete nic, uloží se 0")
    print(f"4. Zavřete okno pro zobrazení výsledků")
    
    print(f"\n🚀 Spouštím viewer...")
    
    # Test s return_results=True
    result_df = preview.show_document_qt(file_path, df, return_results=True)
    
    print(f"\n📊 Analýza číselných výsledků:")
    if result_df is not None and 'result_class' in result_df.columns:
        # Zobrazíme typy dat v result_class sloupci
        print(f"✅ result_class sloupec existuje!")
        print(f"✅ Typ dat v result_class: {result_df['result_class'].dtype}")
        
        # Statistiky
        total = len(result_df)
        classified = len(result_df[result_df['result_class'] > 0])
        unclassified = len(result_df[result_df['result_class'] == 0])
        
        print(f"✅ Celkem prvků: {total}")
        print(f"✅ Klasifikovaných (result_class > 0): {classified}")
        print(f"✅ Neklasifikovaných (result_class = 0): {unclassified}")
        
        # Rozložení podle číselných hodnot
        if classified > 0:
            print(f"\n🔢 Rozložení podle číselných hodnot:")
            result_counts = result_df[result_df['result_class'] > 0]['result_class'].value_counts().sort_index()
            
            category_mapping = {cat_id: cat_text for cat_id, cat_text in categories}
            
            for class_id, count in result_counts.items():
                class_text = utils.get_result_class_name(class_id)
                print(f"   {class_id} ({class_text}): {count} prvků")
            
            # Ukázka klasifikovaných prvků
            print(f"\n🔍 Ukázka klasifikovaných prvků:")
            sample = result_df[result_df['result_class'] > 0][['text', 'value_class', 'result_class']].head(5)
            for _, row in sample.iterrows():
                class_text = utils.get_result_class_name(row['result_class'])
                print(f"   '{row['text']}' → {row['result_class']} ({class_text})")
            
            # Ověření datových typů
            print(f"\n🔍 Ověření datových typů:")
            sample_values = result_df[result_df['result_class'] > 0]['result_class'].head(3)
            for val in sample_values:
                print(f"   Hodnota: {val}, Typ: {type(val)}")
        
        # Export pro kontrolu
        result_df.to_csv('numeric_test_results.csv', index=False)
        print(f"\n💾 Výsledky uloženy do: numeric_test_results.csv")
        
        return True
    else:
        print(f"❌ Problém s result_class sloupcem!")
        return False

if __name__ == "__main__":
    success = test_numeric_values()
    if success:
        print(f"\n✅ Test číselných hodnot úspěšný!")
    else:
        print(f"\n❌ Test číselných hodnot neúspěšný!")
